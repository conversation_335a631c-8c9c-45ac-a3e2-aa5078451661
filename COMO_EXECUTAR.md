# 🚀 Como Executar o Agente GA4

## 📁 Arquivos Disponíveis

Criei 3 formas diferentes de executar o agente:

### 1. `iniciar_agente.bat` - **Recomendado**
- **Arquivo .bat melhorado**
- Interface mais clara e robusta
- Funciona no CMD e PowerShell

### 2. `agente_ga4.ps1` - **PowerShell**
- **Script PowerShell nativo**
- Interface colorida e mais bonita
- Melhor tratamento de erros

### 3. `agente_ga4.bat` - **Original**
- Versão original (pode ter problemas)

## 🎯 Como Usar

### Opção 1: Arquivo .BAT (Mais Simples)

**Duplo clique em:** `iniciar_agente.bat`

Ou no terminal:
```cmd
iniciar_agente.bat
```

### Opção 2: PowerShell (Mais <PERSON>)

**No PowerShell:**
```powershell
.\agente_ga4.ps1
```

Se der erro de execução, execute primeiro:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 🔧 O que Acontece Quando Executar

1. **Verificação do ambiente**
   - Ativa ambiente virtual (se existir)
   - Verifica se Python está instalado
   - Verifica se credenciais GA4 existem

2. **Configuração**
   - Solicita chave OpenAI (se não configurada)
   - Solicita Property ID GA4 (opcional)
   - Confirma configurações

3. **Menu principal**
   - Opções para testes, uso interativo, consultas rápidas

## 💬 Fluxo de Uso Típico

### Primeira Vez
1. Execute `iniciar_agente.bat`
2. Digite sua chave OpenAI quando solicitado
3. Digite seu Property ID GA4 (ou Enter para pular)
4. Escolha opção "1" para executar testes
5. Se testes passarem, use opção "2" para modo interativo

### Uso Diário
1. Execute `iniciar_agente.bat`
2. Escolha opção "3" para consulta rápida
3. Digite sua pergunta sobre dados GA4

## 🔑 Informações Necessárias

### Chave OpenAI (Obrigatória)
- Formato: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- Obtenha em: https://platform.openai.com/api-keys

### Property ID GA4 (Opcional)
- Formato: `123456789`
- Encontre no Google Analytics 4 > Admin > Informações da propriedade
- Se não informar, será solicitado em cada consulta

### Credenciais GA4 (Obrigatória)
- Arquivo: `credenciais-ga4.json`
- Deve estar no mesmo diretório dos scripts

## 💡 Exemplos de Perguntas

Quando usar o agente, experimente:

- "Quantos usuários ativos tivemos ontem?"
- "Mostre-me os usuários por país dos últimos 30 dias"
- "Quais são as páginas mais visitadas esta semana?"
- "Quantos usuários estão online agora?"
- "Qual é a taxa de conversão atual?"
- "Faça uma análise completa da audiência dos últimos 30 dias"

## 🐛 Solução de Problemas

### ❌ Script fecha imediatamente
- **Causa**: Erro na configuração ou falta de dependências
- **Solução**: Execute no terminal para ver erros detalhados

### ❌ "Python não encontrado"
- **Causa**: Python não instalado ou não no PATH
- **Solução**: Instale Python de python.org

### ❌ "credenciais-ga4.json não encontrado"
- **Causa**: Arquivo de credenciais não existe
- **Solução**: Baixe do Google Cloud Console e salve no diretório

### ❌ "Chave OpenAI inválida"
- **Causa**: Chave incorreta ou sem créditos
- **Solução**: Verifique a chave e créditos em platform.openai.com

### ❌ PowerShell não executa .ps1
- **Causa**: Política de execução restritiva
- **Solução**: Execute `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser`

## 🎉 Teste Rápido

Para testar se tudo está funcionando:

1. Execute `iniciar_agente.bat`
2. Configure suas chaves
3. Escolha opção "1" (Executar testes)
4. Se 4/5 ou 5/5 testes passarem, está funcionando!

## 📞 Próximos Passos

1. **Teste primeiro**: Execute testes para verificar funcionamento
2. **Use modo interativo**: Para conversas longas sobre dados
3. **Use consulta rápida**: Para perguntas específicas
4. **Explore exemplos**: Para ver o que é possível fazer

**🎯 Agora você tem múltiplas formas de executar o agente! Escolha a que preferir e comece a analisar seus dados do GA4!**
