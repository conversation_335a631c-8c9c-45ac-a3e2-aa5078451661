# 🌐 Guia: GA4 Python MCP com Cloudflare Tunnel

## 🎯 Objetivo

Este guia mostra como usar o servidor GA4 Python MCP com túnel Cloudflare, tornando-o acessível publicamente para a Evolution API ou qualquer outro serviço externo.

## 🚀 Início Rápido

### Opção 1: Script Automático (<PERSON>s <PERSON>)

```bash
# Windows (Duplo clique)
start_tunnel.bat

# Ou PowerShell
.\start_tunnel.ps1

# Ou Python direto
python cloudflare_tunnel.py
```

### Opção 2: Manual

```bash
# 1. Instalar cloudflared (se necessário)
# O script faz isso automaticamente

# 2. Iniciar servidor + túnel
python cloudflare_tunnel.py --port 8080
```

## 📋 O Que Acontece

1. **Verifica cloudflared** - Se não estiver instalado, baixa automaticamente
2. **Inicia servidor HTTP** - Na porta 8080 (configurável)
3. **Cria túnel Cloudflare** - Gera URL pública (ex: https://abc123.trycloudflare.com)
4. **Testa conexão** - Valida se tudo está funcionando
5. **Exibe informações** - URL pública e endpoints disponíveis

## 🌐 URLs Geradas

Após iniciar, você terá:

```
🎉 Sistema iniciado com sucesso!
============================================================
🌐 URL Pública: https://abc123-def456.trycloudflare.com
🏠 Porta Local: 8080
❤️ Health Check: https://abc123-def456.trycloudflare.com/health
📊 Endpoint GA4: https://abc123-def456.trycloudflare.com/ga4-evolution
============================================================
```

## 🔧 Usando na Evolution API

### Método 1: URL Dinâmica (Recomendado)

Use o arquivo `evolution_final.js` e passe a URL do túnel:

```javascript
// Na Evolution API
const input = {
  property_id: "330715799",
  action: "report",
  metrics: ["activeUsers", "sessions"],
  dimensions: ["date"],
  date_range: "last7days",
  server_url: "https://abc123-def456.trycloudflare.com"  // URL do túnel
};
```

### Método 2: Arquivo de Configuração

O sistema salva automaticamente as informações em `tunnel_info.json`:

```json
{
  "tunnel_url": "https://abc123-def456.trycloudflare.com",
  "local_port": 8080,
  "health_endpoint": "https://abc123-def456.trycloudflare.com/health",
  "ga4_endpoint": "https://abc123-def456.trycloudflare.com/ga4-evolution",
  "status": "active"
}
```

Você pode ler este arquivo na Evolution API para obter a URL automaticamente.

## 🧪 Testando o Túnel

### Teste Manual

```bash
# Health check
curl https://abc123-def456.trycloudflare.com/health

# Dados GA4
curl -X POST https://abc123-def456.trycloudflare.com/ga4-evolution \
  -H "Content-Type: application/json" \
  -d '{"property_id": "330715799", "action": "report", "metrics": ["activeUsers"]}'
```

### Teste Automático

```bash
# Testa se cloudflared está disponível
python cloudflare_tunnel.py --test-only

# Executa testes completos
python test_integration.py
```

## ⚙️ Configurações Avançadas

### Porta Personalizada

```bash
python cloudflare_tunnel.py --port 9000
```

### Configuração Permanente

Para usar sempre a mesma URL, configure um túnel nomeado:

1. **Instale cloudflared** globalmente
2. **Faça login** no Cloudflare:
   ```bash
   cloudflared tunnel login
   ```
3. **Crie túnel nomeado**:
   ```bash
   cloudflared tunnel create ga4-mcp
   ```
4. **Configure DNS** no painel Cloudflare
5. **Use túnel nomeado**:
   ```bash
   cloudflared tunnel run ga4-mcp
   ```

### Variáveis de Ambiente

```bash
# .env
CLOUDFLARE_TUNNEL_URL=https://ga4-mcp.seudominio.com
GA4_PROPERTY_ID=330715799
TUNNEL_PORT=8080
```

## 🔒 Segurança

### ✅ Vantagens do Cloudflare Tunnel

- **HTTPS automático** - Certificado SSL gratuito
- **Proteção DDoS** - Cloudflare protege contra ataques
- **Sem porta aberta** - Não precisa abrir portas no firewall
- **URL aleatória** - Dificulta acesso não autorizado

### ⚠️ Considerações

- **URL temporária** - URLs gratuitas mudam a cada reinicialização
- **Limite de uso** - Cloudflare pode ter limites para uso gratuito
- **Dependência externa** - Requer internet e Cloudflare funcionando

### 🛡️ Recomendações

1. **Use autenticação** se necessário
2. **Monitore logs** para detectar uso indevido
3. **Configure rate limiting** se necessário
4. **Use túnel nomeado** para produção

## 🚨 Solução de Problemas

### Erro: "cloudflared não encontrado"

```bash
# O script baixa automaticamente, mas se falhar:
# 1. Baixe manualmente de: https://github.com/cloudflare/cloudflared/releases
# 2. Coloque cloudflared.exe no diretório do projeto
# 3. Execute novamente
```

### Erro: "Túnel não conecta"

```bash
# 1. Verifique internet
ping cloudflare.com

# 2. Verifique firewall
# Permita cloudflared.exe no Windows Defender

# 3. Tente porta diferente
python cloudflare_tunnel.py --port 9000
```

### Erro: "Servidor não responde"

```bash
# 1. Verifique se o servidor local está rodando
curl http://localhost:8080/health

# 2. Verifique logs do servidor
# Os logs aparecem no terminal

# 3. Reinicie o sistema
# Pare com Ctrl+C e execute novamente
```

### URL do túnel muda sempre

```bash
# Para URL fixa, configure túnel nomeado:
cloudflared tunnel login
cloudflared tunnel create ga4-mcp
# Configure DNS no painel Cloudflare
```

## 📊 Monitoramento

### Logs do Sistema

O sistema gera logs detalhados:

```
INFO:cloudflare-tunnel:🚀 Iniciando servidor HTTP na porta 8080...
INFO:cloudflare-tunnel:✅ Servidor HTTP iniciado com sucesso
INFO:cloudflare-tunnel:🌐 Iniciando túnel Cloudflare...
INFO:cloudflare-tunnel:✅ Túnel criado: https://abc123.trycloudflare.com
INFO:cloudflare-tunnel:🧪 Testando túnel...
INFO:cloudflare-tunnel:✅ Túnel funcionando corretamente
```

### Arquivo de Status

```json
// tunnel_info.json (atualizado automaticamente)
{
  "tunnel_url": "https://abc123.trycloudflare.com",
  "local_port": 8080,
  "health_endpoint": "https://abc123.trycloudflare.com/health",
  "ga4_endpoint": "https://abc123.trycloudflare.com/ga4-evolution",
  "status": "active",
  "started_at": "2024-01-01T12:00:00Z"
}
```

## 🎯 Casos de Uso

### 1. Evolution API Remota

```javascript
// Script Evolution API
const tunnelInfo = JSON.parse(fs.readFileSync('tunnel_info.json'));
const input = {
  server_url: tunnelInfo.tunnel_url,
  property_id: "330715799",
  action: "report"
};
```

### 2. Webhook/API Externa

```bash
# Qualquer serviço pode acessar
POST https://abc123.trycloudflare.com/ga4-evolution
Content-Type: application/json

{
  "property_id": "330715799",
  "action": "realtime",
  "metrics": ["activeUsers"]
}
```

### 3. Dashboard Web

```html
<!-- Página web pode acessar diretamente -->
<script>
fetch('https://abc123.trycloudflare.com/ga4-evolution', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    property_id: '330715799',
    action: 'report',
    metrics: ['activeUsers']
  })
})
.then(response => response.json())
.then(data => console.log(data));
</script>
```

## 🎉 Conclusão

O túnel Cloudflare torna o servidor GA4 Python MCP acessível globalmente de forma segura e gratuita. É a solução ideal para:

- ✅ **Evolution API remota**
- ✅ **Integrações externas**
- ✅ **Desenvolvimento e testes**
- ✅ **Demonstrações**

**🚀 Execute `start_tunnel.bat` e comece a usar imediatamente!**
