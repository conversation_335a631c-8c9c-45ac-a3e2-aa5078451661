# Agente GA4 com Lang<PERSON>hain

Este projeto integra o servidor MCP (Model Context Protocol) do Google Analytics 4 com Lang<PERSON>hain para criar um agente conversacional que permite fazer perguntas em linguagem natural sobre dados do GA4.

## 🚀 Funcionalidades

- **Consultas em linguagem natural**: Faça perguntas sobre seus dados do GA4 em português
- **Relatórios automáticos**: Gere relatórios históricos e em tempo real
- **Análises inteligentes**: O agente interpreta os dados e fornece insights
- **Múltiplas métricas**: Acesse usuários ativos, sessões, conversões, receita, etc.
- **Dimensões variadas**: Analise por país, dispositivo, canal de aquisição, etc.

## 📋 Pré-requisitos

1. **Credenciais do Google Analytics 4**:
   - Arquivo `credenciais-ga4.json` com as credenciais de service account
   - ID da propriedade GA4

2. **Chave da API OpenAI**:
   - Conta OpenAI com acesso à API
   - Chave de API válida

3. **Python 3.9+** com as dependências instaladas

## 🛠️ Instalação

1. **Clone ou baixe os arquivos**:
   ```bash
   # Os arquivos necessários:
   # - langchain_ga4_client.py
   # - langchain_ga4_tools.py
   # - ga4_agent.py
   # - exemplo_ga4_agent.py
   ```

2. **Instale as dependências**:
   ```bash
   pip install langchain langchain-openai langchain-community langchain-core httpx
   ```

3. **Configure as variáveis de ambiente**:
   ```bash
   # Windows
   set OPENAI_API_KEY=sua_chave_openai_aqui
   set GA4_PROPERTY_ID=seu_property_id_aqui

   # Linux/Mac
   export OPENAI_API_KEY=sua_chave_openai_aqui
   export GA4_PROPERTY_ID=seu_property_id_aqui
   ```

4. **Coloque o arquivo de credenciais**:
   - Salve suas credenciais do GA4 como `credenciais-ga4.json` no diretório do projeto

## 🎯 Como Usar

### Exemplo Rápido

```python
from ga4_agent import run_ga4_query

# Fazer uma pergunta simples
resposta = run_ga4_query(
    "Quantos usuários ativos tivemos nos últimos 7 dias?",
    property_id="seu_property_id",
    openai_api_key="sua_chave_openai"
)
print(resposta)
```

### Uso Programático Simples

```python
# Consulta rápida
from ga4_agent import run_ga4_query

resposta = run_ga4_query("Usuários ativos ontem")
print(resposta)
```

### Exemplo Completo

```python
import asyncio
from ga4_agent import GA4AgentContext

async def main():
    async with GA4AgentContext(
        property_id="seu_property_id",
        openai_api_key="sua_chave_openai"
    ) as agent:
        
        # Fazer perguntas
        resposta1 = await agent.chat("Mostre-me os usuários por país dos últimos 30 dias")
        print(resposta1)
        
        resposta2 = await agent.chat("Qual é a taxa de conversão atual?")
        print(resposta2)

asyncio.run(main())
```

### Script de Exemplo Interativo

Execute o script de exemplo para testar todas as funcionalidades:

```bash
python exemplo_ga4_agent.py
```

## 💬 Exemplos de Perguntas

### Análise de Audiência
- "Quantos usuários ativos tivemos ontem?"
- "Mostre-me os usuários por país dos últimos 30 dias"
- "Qual é o número de sessões por dispositivo esta semana?"

### Análise de Comportamento
- "Quais são as páginas mais visitadas dos últimos 7 dias?"
- "Qual é a taxa de rejeição do site?"
- "Quanto tempo os usuários ficam no site em média?"

### Análise de Conversões
- "Quantas conversões tivemos este mês?"
- "Qual é a receita total dos últimos 30 dias?"
- "Qual canal de aquisição gera mais conversões?"

### Dados em Tempo Real
- "Quantos usuários estão online agora?"
- "Quais páginas estão sendo visitadas neste momento?"
- "De quais países vêm os usuários ativos agora?"

### Análise de Canais
- "Qual é o desempenho dos canais de aquisição?"
- "Quantos usuários vieram do Google esta semana?"
- "Como está o tráfego das redes sociais?"

## 🔧 Configuração Avançada

### Personalizar o Modelo

```python
from ga4_agent import GA4Agent

agent = GA4Agent(
    property_id="seu_property_id",
    openai_api_key="sua_chave",
    model_name="gpt-4",  # ou "gpt-3.5-turbo"
    temperature=0.1      # controla criatividade
)
```

### Usar Múltiplas Propriedades

```python
# Especificar property_id em cada pergunta
resposta = await agent.chat(
    "Usuários ativos dos últimos 7 dias",
    property_id="outra_propriedade"
)
```

## 📊 Métricas e Dimensões Disponíveis

### Métricas Principais
- `activeUsers`: Usuários únicos ativos
- `sessions`: Sessões/visitas
- `screenPageViews`: Visualizações de página
- `conversions`: Conversões
- `totalRevenue`: Receita total
- `bounceRate`: Taxa de rejeição
- `sessionDuration`: Duração da sessão

### Dimensões Principais
- `date`: Data
- `country`, `city`: Localização
- `deviceCategory`: Tipo de dispositivo
- `sessionDefaultChannelGroup`: Canal de aquisição
- `pagePath`: Caminho da página
- `eventName`: Nome do evento

Para ver todas as métricas e dimensões disponíveis:
```python
resposta = await agent.chat("Quais métricas estão disponíveis?")
```

## 🐛 Solução de Problemas

### Erro de Autenticação
- Verifique se o arquivo `credenciais-ga4.json` está correto
- Confirme se a service account tem acesso à propriedade GA4

### Erro de API OpenAI
- Verifique se a chave da API está correta
- Confirme se há créditos disponíveis na conta OpenAI

### Servidor MCP não inicia
- Verifique se todas as dependências estão instaladas
- Confirme se o módulo `mcp_server_ga4` está no PATH

### Dados não aparecem
- Verifique se o property_id está correto
- Confirme se há dados na propriedade GA4 para o período solicitado

## 📝 Estrutura do Projeto

```
├── langchain_ga4_client.py    # Cliente MCP para LangChain
├── langchain_ga4_tools.py     # Ferramentas LangChain para GA4
├── ga4_agent.py              # Agente conversacional principal
├── exemplo_ga4_agent.py      # Script de exemplo
├── credenciais-ga4.json      # Credenciais do GA4 (você deve criar)
└── mcp_server_ga4/           # Servidor MCP do GA4 (já existente)
```

## 🤝 Contribuição

Este projeto integra:
- **MCP Server GA4**: Servidor que conecta ao Google Analytics 4
- **LangChain**: Framework para aplicações com LLM
- **OpenAI GPT**: Modelo de linguagem para interpretação

## 📄 Licença

Este projeto segue a mesma licença do servidor MCP GA4 original.
