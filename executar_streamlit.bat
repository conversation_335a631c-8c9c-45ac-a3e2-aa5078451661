@echo off
setlocal enabledelayedexpansion
title Agente GA4 - Interface Web

echo.
echo ========================================
echo    AGENTE GA4 - INTERFACE WEB
echo ========================================
echo.

REM Ativar ambiente virtual se existir
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Ativando ambiente virtual...
    call venv\Scripts\activate.bat
    echo.
)

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERRO] Python nao encontrado. Instale Python primeiro.
    pause
    exit /b 1
)

REM Verificar Streamlit
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Instalando Streamlit...
    pip install streamlit plotly pandas
    echo.
)

REM Verificar credenciais
if not exist "credenciais-ga4.json" (
    echo [AVISO] Arquivo credenciais-ga4.json nao encontrado
    echo         Voce pode configurar depois na interface web
    echo.
)

echo [INFO] Iniciando interface web do Agente GA4...
echo.
echo ========================================
echo    INSTRUCOES
echo ========================================
echo.
echo 1. A interface web sera aberta no navegador
echo 2. Configure sua chave OpenAI na barra lateral
echo 3. Configure o Property ID do GA4 (opcional)
echo 4. Comece a fazer perguntas sobre seus dados!
echo.
echo URL: http://localhost:8501
echo.
echo Para parar: Pressione Ctrl+C neste terminal
echo.
echo ========================================

REM Aguardar um pouco para o usuário ler
timeout /t 3 /nobreak >nul

REM Executar Streamlit
streamlit run app_streamlit.py --server.headless true

echo.
echo Interface web encerrada.
pause
