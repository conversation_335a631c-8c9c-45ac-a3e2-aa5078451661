# 🚀 In<PERSON><PERSON>pido - Agente GA4 com LangChain

## ⚡ Configuração em 5 Minutos

### 1. Configurar Variáveis de Ambiente

**Windows (PowerShell):**
```powershell
$env:OPENAI_API_KEY="sua_chave_openai_aqui"
$env:GA4_PROPERTY_ID="seu_property_id_aqui"
```

**Windows (CMD):**
```cmd
set OPENAI_API_KEY=sua_chave_openai_aqui
set GA4_PROPERTY_ID=seu_property_id_aqui
```

**Linux/Mac:**
```bash
export OPENAI_API_KEY="sua_chave_openai_aqui"
export GA4_PROPERTY_ID="seu_property_id_aqui"
```

### 2. Verificar se Tudo <PERSON>st<PERSON>

```bash
python teste_ga4_agent.py
```

### 3. Começar a Usar

```bash
python exemplo_ga4_agent.py
```

## 🎯 Teste Rápido

```python
from ga4_agent import run_ga4_query

# Substitua pelos seus valores
resposta = run_ga4_query(
    "Quantos usuários ativos tivemos ontem?",
    property_id="SEU_PROPERTY_ID",
    openai_api_key="SUA_CHAVE_OPENAI"
)
print(resposta)
```

## 💬 Perguntas de Exemplo

Experimente estas perguntas:

- "Quantos usuários ativos tivemos nos últimos 7 dias?"
- "Mostre-me os usuários por país dos últimos 30 dias"
- "Quais são as páginas mais visitadas esta semana?"
- "Quantos usuários estão online agora?"
- "Qual é a taxa de conversão atual?"

## 🔧 Solução de Problemas Rápida

### ❌ "OPENAI_API_KEY não configurada"
- Configure a variável de ambiente com sua chave da OpenAI
- Obtenha em: https://platform.openai.com/api-keys

### ❌ "credenciais-ga4.json não encontrado"
- Baixe as credenciais do Google Cloud Console
- Salve como `credenciais-ga4.json` no diretório do projeto

### ❌ "Erro no servidor MCP"
- Verifique se o módulo `mcp_server_ga4` está instalado
- Execute: `pip install -e .` no diretório do projeto

### ❌ "Property ID não configurado"
- Configure GA4_PROPERTY_ID ou passe o property_id em cada consulta
- Encontre seu Property ID no Google Analytics 4

## 📚 Próximos Passos

1. **Leia a documentação completa**: `README_AGENTE_GA4.md`
2. **Explore os exemplos**: `exemplo_ga4_agent.py`
3. **Configure adequadamente**: `config_exemplo.py`
4. **Teste tudo**: `teste_ga4_agent.py`

## 🎉 Pronto!

Agora você pode fazer perguntas em linguagem natural sobre seus dados do Google Analytics 4!
