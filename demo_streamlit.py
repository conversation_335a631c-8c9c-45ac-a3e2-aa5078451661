"""Versão demo do Streamlit com dados simulados para demonstração."""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random

# Configurar página
st.set_page_config(
    page_title="Demo - Agente GA4",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .demo-badge {
        background-color: #ff9800;
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 0.3rem;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
</style>
""", unsafe_allow_html=True)

# Função para gerar dados simulados
@st.cache_data
def generate_demo_data():
    """Gera dados simulados para demonstração."""
    # Dados de usuários por dia (últimos 30 dias)
    dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
    users_data = {
        'date': dates,
        'users': [random.randint(800, 1500) for _ in range(30)],
        'sessions': [random.randint(1000, 2000) for _ in range(30)],
        'pageviews': [random.randint(2000, 4000) for _ in range(30)]
    }
    
    # Dados por país
    countries_data = {
        'country': ['Brasil', 'Estados Unidos', 'Argentina', 'Portugal', 'México'],
        'users': [5420, 2130, 890, 650, 420],
        'sessions': [7200, 2800, 1200, 850, 580]
    }
    
    # Dados por dispositivo
    devices_data = {
        'device': ['Mobile', 'Desktop', 'Tablet'],
        'users': [6800, 4200, 800],
        'percentage': [56.7, 35.0, 6.7]
    }
    
    # Páginas mais visitadas
    pages_data = {
        'page': ['/home', '/produtos', '/sobre', '/contato', '/blog'],
        'pageviews': [12500, 8900, 4200, 3100, 2800],
        'unique_pageviews': [10200, 7100, 3800, 2900, 2400]
    }
    
    return {
        'users_timeline': pd.DataFrame(users_data),
        'countries': pd.DataFrame(countries_data),
        'devices': pd.DataFrame(devices_data),
        'pages': pd.DataFrame(pages_data)
    }

# Função para simular resposta do agente
def simulate_agent_response(question):
    """Simula resposta do agente GA4."""
    question_lower = question.lower()
    
    if 'usuários ativos' in question_lower or 'usuarios ativos' in question_lower:
        return """📊 **Análise de Usuários Ativos**

Nos últimos 7 dias, seu site teve **8.420 usuários ativos únicos**, representando um crescimento de 12% em relação à semana anterior.

**Destaques:**
- 📈 Pico de usuários: 1.450 (terça-feira)
- 📉 Menor atividade: 890 (domingo)
- 🎯 Média diária: 1.203 usuários

**Insights:**
- Terças e quartas-feiras são os dias de maior engajamento
- Finais de semana têm 30% menos atividade
- Tendência de crescimento consistente nos últimos 30 dias"""

    elif 'país' in question_lower or 'pais' in question_lower:
        return """🌍 **Análise Geográfica - Últimos 30 dias**

**Top 5 Países por Usuários:**
1. 🇧🇷 **Brasil**: 5.420 usuários (45.2%)
2. 🇺🇸 **Estados Unidos**: 2.130 usuários (17.8%)
3. 🇦🇷 **Argentina**: 890 usuários (7.4%)
4. 🇵🇹 **Portugal**: 650 usuários (5.4%)
5. 🇲🇽 **México**: 420 usuários (3.5%)

**Insights:**
- Brasil domina com quase metade do tráfego
- Mercado americano representa oportunidade de crescimento
- Forte presença em países de língua portuguesa e espanhola
- 20.7% do tráfego vem de outros países não listados"""

    elif 'dispositivo' in question_lower or 'mobile' in question_lower or 'desktop' in question_lower:
        return """📱 **Análise por Dispositivos**

**Distribuição de Usuários:**
- 📱 **Mobile**: 6.800 usuários (56.7%)
- 💻 **Desktop**: 4.200 usuários (35.0%)
- 📟 **Tablet**: 800 usuários (6.7%)

**Performance por Dispositivo:**
- **Mobile**: Taxa de rejeição 45%, Tempo médio 2:30min
- **Desktop**: Taxa de rejeição 35%, Tempo médio 4:15min
- **Tablet**: Taxa de rejeição 40%, Tempo médio 3:45min

**Insights:**
- Maioria dos usuários acessa via mobile
- Desktop tem melhor engajamento (menor rejeição, mais tempo)
- Importante otimizar experiência mobile"""

    elif 'páginas' in question_lower or 'paginas' in question_lower:
        return """📄 **Páginas Mais Visitadas - Esta Semana**

**Top 5 Páginas:**
1. 🏠 **/home**: 12.500 visualizações
2. 🛍️ **/produtos**: 8.900 visualizações
3. ℹ️ **/sobre**: 4.200 visualizações
4. 📞 **/contato**: 3.100 visualizações
5. 📝 **/blog**: 2.800 visualizações

**Métricas de Engajamento:**
- Página inicial tem 82% de visualizações únicas
- Produtos: tempo médio de 3:45min na página
- Blog: maior taxa de compartilhamento (15%)

**Recomendações:**
- Otimizar call-to-action na página inicial
- Melhorar navegação de produtos para contato"""

    elif 'tempo real' in question_lower or 'online' in question_lower:
        return """⏱️ **Dados em Tempo Real**

**Usuários Ativos Agora:** 127 usuários

**Atividade Atual:**
- 🌍 **Países ativos**: Brasil (78), EUA (23), Argentina (15), outros (11)
- 📱 **Dispositivos**: Mobile (89), Desktop (32), Tablet (6)
- 📄 **Páginas ativas**: /home (45), /produtos (28), /blog (18), outras (36)

**Últimos 30 minutos:**
- 📊 Visualizações de página: 342
- 🎯 Eventos: 156
- 🔄 Taxa de rejeição: 38%

**Tendência:** Atividade 15% acima da média para este horário"""

    elif 'conversão' in question_lower or 'conversao' in question_lower:
        return """💰 **Análise de Conversões - Últimos 30 dias**

**Taxa de Conversão Geral:** 3.2%

**Conversões por Canal:**
- 🔍 **Busca Orgânica**: 4.1% (melhor performance)
- 📱 **Redes Sociais**: 2.8%
- 💰 **Anúncios Pagos**: 3.9%
- 📧 **Email Marketing**: 5.2% (maior ROI)
- 🔗 **Referências**: 2.1%

**Objetivos Atingidos:**
- 📞 Contatos: 245 conversões
- 🛒 Compras: 89 conversões
- 📧 Newsletter: 156 inscrições

**Insights:**
- Email marketing tem melhor taxa de conversão
- Busca orgânica traz tráfego qualificado
- Oportunidade de melhorar conversão em redes sociais"""

    else:
        return f"""🤖 **Análise Personalizada**

Analisei sua pergunta: "{question}"

**Resumo dos Dados:**
- 📊 Usuários ativos (30 dias): 12.000
- 📈 Crescimento mensal: +15%
- 🌍 Principal mercado: Brasil (45%)
- 📱 Dispositivo principal: Mobile (57%)
- 💰 Taxa de conversão: 3.2%

**Recomendações:**
- Continue focando no mercado brasileiro
- Otimize a experiência mobile
- Explore oportunidades em email marketing
- Monitore tendências de crescimento

Para análises mais específicas, faça perguntas sobre usuários, países, dispositivos, páginas, tempo real ou conversões."""

def main():
    """Função principal da aplicação demo."""
    
    # Header
    st.markdown('<h1 class="main-header">📊 Agente GA4 - Demo Interativo</h1>', unsafe_allow_html=True)
    st.markdown('<div style="text-align: center;"><span class="demo-badge">DEMONSTRAÇÃO</span></div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Gerar dados demo
    data = generate_demo_data()
    
    # Sidebar
    with st.sidebar:
        st.header("📊 Demo - Dados Simulados")
        st.info("Esta é uma demonstração com dados simulados para mostrar as funcionalidades da interface.")
        
        st.subheader("📈 Métricas Rápidas")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Usuários", "12.0K", "+15%")
            st.metric("Sessões", "18.5K", "+12%")
        with col2:
            st.metric("Pageviews", "45.2K", "+8%")
            st.metric("Taxa Conv.", "3.2%", "+0.3%")
    
    # Tabs principais
    tab1, tab2, tab3 = st.tabs(["💬 Chat Demo", "📊 Dashboard", "📈 Relatórios"])
    
    with tab1:
        st.subheader("💬 Chat com Dados Simulados")
        st.info("💡 Experimente perguntas como: 'Quantos usuários ativos tivemos?', 'Mostre dados por país', 'Análise de dispositivos'")
        
        # Chat interface
        if 'demo_messages' not in st.session_state:
            st.session_state.demo_messages = []
        
        # Exibir mensagens
        for message in st.session_state.demo_messages:
            if message['role'] == 'user':
                st.chat_message("user").write(message['content'])
            else:
                st.chat_message("assistant").write(message['content'])
        
        # Input do usuário
        if prompt := st.chat_input("Faça uma pergunta sobre os dados..."):
            st.session_state.demo_messages.append({"role": "user", "content": prompt})
            st.chat_message("user").write(prompt)
            
            # Simular resposta
            with st.chat_message("assistant"):
                with st.spinner("Analisando dados..."):
                    response = simulate_agent_response(prompt)
                st.write(response)
            
            st.session_state.demo_messages.append({"role": "assistant", "content": response})
    
    with tab2:
        st.subheader("📊 Dashboard Interativo")
        
        # Métricas principais
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("👥 Usuários Ativos", "12.0K", "+15%")
        with col2:
            st.metric("📊 Sessões", "18.5K", "+12%")
        with col3:
            st.metric("📄 Pageviews", "45.2K", "+8%")
        with col4:
            st.metric("💰 Conversões", "3.2%", "+0.3%")
        
        # Gráficos
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📈 Usuários ao Longo do Tempo")
            fig_timeline = px.line(data['users_timeline'], x='date', y='users', 
                                 title="Usuários Diários - Últimos 30 dias")
            st.plotly_chart(fig_timeline, use_container_width=True)
            
            st.subheader("🌍 Usuários por País")
            fig_countries = px.bar(data['countries'], x='country', y='users',
                                 title="Top 5 Países")
            st.plotly_chart(fig_countries, use_container_width=True)
        
        with col2:
            st.subheader("📱 Distribuição por Dispositivos")
            fig_devices = px.pie(data['devices'], values='users', names='device',
                                title="Usuários por Dispositivo")
            st.plotly_chart(fig_devices, use_container_width=True)
            
            st.subheader("📄 Páginas Mais Visitadas")
            fig_pages = px.bar(data['pages'], x='pageviews', y='page',
                              orientation='h', title="Top 5 Páginas")
            st.plotly_chart(fig_pages, use_container_width=True)
    
    with tab3:
        st.subheader("📈 Relatórios Detalhados")
        
        # Seletor de relatório
        report_type = st.selectbox(
            "Escolha o tipo de relatório:",
            ["Usuários por País", "Dispositivos", "Páginas Populares", "Timeline de Usuários"]
        )
        
        if report_type == "Usuários por País":
            st.dataframe(data['countries'], use_container_width=True)
            
        elif report_type == "Dispositivos":
            st.dataframe(data['devices'], use_container_width=True)
            
        elif report_type == "Páginas Populares":
            st.dataframe(data['pages'], use_container_width=True)
            
        elif report_type == "Timeline de Usuários":
            st.dataframe(data['users_timeline'], use_container_width=True)
        
        # Botão de download
        if st.button("📥 Download Relatório (CSV)"):
            if report_type == "Usuários por País":
                csv = data['countries'].to_csv(index=False)
            elif report_type == "Dispositivos":
                csv = data['devices'].to_csv(index=False)
            elif report_type == "Páginas Populares":
                csv = data['pages'].to_csv(index=False)
            else:
                csv = data['users_timeline'].to_csv(index=False)
            
            st.download_button(
                label="📥 Baixar CSV",
                data=csv,
                file_name=f"relatorio_{report_type.lower().replace(' ', '_')}.csv",
                mime="text/csv"
            )
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        🎯 Esta é uma demonstração com dados simulados | 
        <a href="README_STREAMLIT.md">Ver documentação completa</a>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
