# Agente GA4 com <PERSON><PERSON><PERSON>n - Script PowerShell
# Configuração e execução do agente conversacional

$Host.UI.RawUI.WindowTitle = "Agente GA4 com <PERSON><PERSON>hain"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AGENTE GA4 COM LANGCHAIN" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Ativar ambiente virtual se existir
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "[INFO] Ativando ambiente virtual..." -ForegroundColor Green
    & "venv\Scripts\Activate.ps1"
    Write-Host ""
}

# Verificar Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[OK] Python encontrado: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERRO] Python não encontrado. Instale Python primeiro." -ForegroundColor Red
    Read-Host "Pressione Enter para sair"
    exit 1
}

# Verificar credenciais GA4
if (-not (Test-Path "credenciais-ga4.json")) {
    Write-Host "[ERRO] Arquivo credenciais-ga4.json não encontrado" -ForegroundColor Red
    Write-Host "       Baixe as credenciais do Google Cloud Console" -ForegroundColor Yellow
    Write-Host "       e salve como 'credenciais-ga4.json'" -ForegroundColor Yellow
    Read-Host "Pressione Enter para sair"
    exit 1
}

Write-Host "[OK] Credenciais GA4 encontradas" -ForegroundColor Green
Write-Host ""

# Configurar chave OpenAI
if (-not $env:OPENAI_API_KEY) {
    Write-Host "[CONFIG] Chave OpenAI não configurada" -ForegroundColor Yellow
    Write-Host ""
    $openaiKey = Read-Host "Digite sua chave da API OpenAI"
    
    if (-not $openaiKey) {
        Write-Host "[ERRO] Chave OpenAI é obrigatória" -ForegroundColor Red
        Read-Host "Pressione Enter para sair"
        exit 1
    }
    
    $env:OPENAI_API_KEY = $openaiKey
    Write-Host "[OK] Chave OpenAI configurada" -ForegroundColor Green
} else {
    Write-Host "[OK] Chave OpenAI já configurada" -ForegroundColor Green
}

Write-Host ""

# Configurar Property ID (opcional)
if (-not $env:GA4_PROPERTY_ID) {
    Write-Host "[CONFIG] Property ID do GA4 não configurado" -ForegroundColor Yellow
    Write-Host ""
    $propertyId = Read-Host "Digite o ID da propriedade GA4 (ou Enter para pular)"
    
    if ($propertyId) {
        $env:GA4_PROPERTY_ID = $propertyId
        Write-Host "[OK] Property ID configurado: $propertyId" -ForegroundColor Green
    } else {
        Write-Host "[INFO] Property ID não configurado - será solicitado em cada consulta" -ForegroundColor Yellow
    }
} else {
    Write-Host "[OK] Property ID já configurado: $($env:GA4_PROPERTY_ID)" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    CONFIGURAÇÃO COMPLETA" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Menu principal
do {
    Write-Host "MENU PRINCIPAL:" -ForegroundColor White
    Write-Host ""
    Write-Host "1. Executar testes do sistema" -ForegroundColor White
    Write-Host "2. Agente interativo (conversa)" -ForegroundColor White
    Write-Host "3. Consulta rápida" -ForegroundColor White
    Write-Host "4. Exemplos de uso" -ForegroundColor White
    Write-Host "5. Sair" -ForegroundColor White
    Write-Host ""
    
    $opcao = Read-Host "Escolha uma opção (1-5)"
    
    switch ($opcao) {
        "1" {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host "    EXECUTANDO TESTES" -ForegroundColor Yellow
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host ""
            python teste_ga4_agent.py
            Write-Host ""
            Read-Host "Pressione Enter para voltar ao menu"
        }
        
        "2" {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host "    AGENTE INTERATIVO" -ForegroundColor Yellow
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "Iniciando agente conversacional..." -ForegroundColor Green
            Write-Host "Digite suas perguntas sobre dados do GA4" -ForegroundColor Yellow
            Write-Host "Digite 'sair' para voltar ao menu" -ForegroundColor Yellow
            Write-Host ""
            python exemplo_ga4_agent.py
            Write-Host ""
            Read-Host "Pressione Enter para voltar ao menu"
        }
        
        "3" {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host "    CONSULTA RÁPIDA" -ForegroundColor Yellow
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host ""
            
            do {
                $pergunta = Read-Host "Digite sua pergunta sobre dados do GA4"
            } while (-not $pergunta)
            
            Write-Host ""
            Write-Host "[INFO] Processando pergunta: '$pergunta'" -ForegroundColor Green
            Write-Host ""
            Write-Host "----------------------------------------" -ForegroundColor Gray
            
            $comando = @"
from ga4_agent import run_ga4_query
import os
print('RESPOSTA:')
print('=' * 50)
try:
    resposta = run_ga4_query('$pergunta', property_id=os.getenv('GA4_PROPERTY_ID'), openai_api_key=os.getenv('OPENAI_API_KEY'))
    print(resposta)
except Exception as e:
    print(f'Erro: {e}')
print('=' * 50)
"@
            
            python -c $comando
            Write-Host "----------------------------------------" -ForegroundColor Gray
            Write-Host ""
            Read-Host "Pressione Enter para voltar ao menu"
        }
        
        "4" {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host "    EXEMPLOS DE USO" -ForegroundColor Yellow
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "Executando exemplos automáticos..." -ForegroundColor Green
            
            $comando = @"
from ga4_agent import run_ga4_query
import os

perguntas = [
    'Quantos usuários ativos tivemos ontem?',
    'Mostre-me as sessões dos últimos 7 dias'
]

for i, pergunta in enumerate(perguntas, 1):
    print(f'\nExemplo {i}: {pergunta}')
    print('-' * 50)
    try:
        resposta = run_ga4_query(pergunta, property_id=os.getenv('GA4_PROPERTY_ID'), openai_api_key=os.getenv('OPENAI_API_KEY'))
        print(resposta[:200] + '...' if len(resposta) > 200 else resposta)
    except Exception as e:
        print(f'Erro: {e}')
    print('-' * 50)
"@
            
            python -c $comando
            Write-Host ""
            Read-Host "Pressione Enter para voltar ao menu"
        }
        
        "5" {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host "    OBRIGADO POR USAR O AGENTE GA4!" -ForegroundColor Yellow
            Write-Host "========================================" -ForegroundColor Cyan
            Write-Host ""
            Read-Host "Pressione Enter para sair"
            exit 0
        }
        
        default {
            Write-Host ""
            Write-Host "[ERRO] Opção inválida. Tente novamente." -ForegroundColor Red
            Write-Host ""
        }
    }
} while ($true)
