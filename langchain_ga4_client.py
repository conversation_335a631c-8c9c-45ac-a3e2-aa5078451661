"""Cliente MCP para Lang<PERSON>hain que se conecta ao servidor GA4."""
import asyncio
import json
import logging
import subprocess
import sys
from typing import Any, Dict, List, Optional, Union
import httpx
import time

logger = logging.getLogger("langchain-ga4-client")


class MCPClient:
    """Cliente para comunicação com servidor MCP via stdio."""
    
    def __init__(self, server_command: List[str]):
        """
        Inicializa o cliente MCP.
        
        Args:
            server_command: Comando para iniciar o servidor MCP
        """
        self.server_command = server_command
        self.process = None
        self.request_id = 0
        
    async def start(self):
        """Inicia o servidor MCP."""
        try:
            self.process = await asyncio.create_subprocess_exec(
                *self.server_command,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            logger.info("Servidor MCP iniciado")

            # Aguardar um pouco para o servidor inicializar
            await asyncio.sleep(1)

            # Testar conexão listando ferramentas
            try:
                tools = await self.list_tools()
                logger.info(f"Conexão estabelecida. Ferramentas disponíveis: {len(tools)}")
            except Exception as e:
                logger.warning(f"Aviso ao testar conexão: {e}")

        except Exception as e:
            logger.error(f"Erro ao iniciar servidor MCP: {e}")
            raise
    
    async def stop(self):
        """Para o servidor MCP."""
        if self.process:
            try:
                self.process.terminate()
                await self.process.wait()
                logger.info("Servidor MCP parado")
            except Exception as e:
                logger.error(f"Erro ao parar servidor MCP: {e}")
    
    async def _send_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Envia uma requisição JSON-RPC para o servidor."""
        if not self.process:
            raise RuntimeError("Servidor MCP não está rodando")

        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params
        }

        # Enviar requisição
        request_json = json.dumps(request) + "\n"
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()

        # Ler resposta
        response_line = await self.process.stdout.readline()
        if not response_line:
            raise RuntimeError("Servidor MCP não respondeu")

        response = json.loads(response_line.decode())

        if "error" in response:
            raise RuntimeError(f"Erro do servidor MCP: {response['error']}")

        # Para o servidor simplificado, a resposta pode estar diretamente no objeto
        return response if "result" not in response else response.get("result", {})
    
    async def _send_notification(self, method: str, params: Dict[str, Any]):
        """Envia uma notificação JSON-RPC para o servidor."""
        if not self.process:
            raise RuntimeError("Servidor MCP não está rodando")
        
        notification = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }
        
        notification_json = json.dumps(notification) + "\n"
        self.process.stdin.write(notification_json.encode())
        await self.process.stdin.drain()
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """Lista as ferramentas disponíveis no servidor."""
        result = await self._send_request("tools/list", {})
        return result.get("tools", [])
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Chama uma ferramenta no servidor."""
        result = await self._send_request("tools/call", {
            "name": name,
            "arguments": arguments
        })
        return result


class GA4MCPClient:
    """Cliente específico para o servidor MCP do GA4."""
    
    def __init__(self, property_id: Optional[str] = None):
        """
        Inicializa o cliente GA4 MCP.
        
        Args:
            property_id: ID da propriedade GA4 (opcional)
        """
        self.property_id = property_id
        self.mcp_client = None
        
    async def start(self):
        """Inicia o cliente MCP."""
        # Comando para iniciar o servidor GA4 MCP
        server_command = [
            sys.executable, "-m", "mcp_server_ga4.main"
        ]
        
        if self.property_id:
            server_command.extend(["--property-id", self.property_id])
        
        self.mcp_client = MCPClient(server_command)
        await self.mcp_client.start()
        
        # Verificar ferramentas disponíveis
        tools = await self.mcp_client.list_tools()
        logger.info(f"Ferramentas GA4 disponíveis: {[tool['name'] for tool in tools]}")
    
    async def stop(self):
        """Para o cliente MCP."""
        if self.mcp_client:
            await self.mcp_client.stop()
    
    async def run_report(
        self,
        metrics: List[str],
        dimensions: Optional[List[str]] = None,
        date_range: Union[Dict[str, str], str] = "last30days",
        property_id: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """
        Executa um relatório GA4.
        
        Args:
            metrics: Lista de métricas
            dimensions: Lista de dimensões (opcional)
            date_range: Período do relatório
            property_id: ID da propriedade (opcional)
            limit: Limite de linhas
            
        Returns:
            Resultado formatado do relatório
        """
        if not self.mcp_client:
            raise RuntimeError("Cliente MCP não está iniciado")
        
        arguments = {
            "metrics": metrics,
            "date_range": date_range,
            "limit": limit
        }
        
        if dimensions:
            arguments["dimensions"] = dimensions
        
        if property_id:
            arguments["property_id"] = property_id
        
        result = await self.mcp_client.call_tool("run_report", arguments)
        return result.get("content", [{}])[0].get("text", "")
    
    async def run_realtime_report(
        self,
        metrics: List[str],
        dimensions: Optional[List[str]] = None,
        property_id: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """
        Executa um relatório em tempo real do GA4.
        
        Args:
            metrics: Lista de métricas
            dimensions: Lista de dimensões (opcional)
            property_id: ID da propriedade (opcional)
            limit: Limite de linhas
            
        Returns:
            Resultado formatado do relatório
        """
        if not self.mcp_client:
            raise RuntimeError("Cliente MCP não está iniciado")
        
        arguments = {
            "metrics": metrics,
            "limit": limit
        }
        
        if dimensions:
            arguments["dimensions"] = dimensions
        
        if property_id:
            arguments["property_id"] = property_id
        
        result = await self.mcp_client.call_tool("run_realtime_report", arguments)
        return result.get("content", [{}])[0].get("text", "")
    
    async def get_metadata(
        self,
        metadata_type: str = "all",
        property_id: Optional[str] = None
    ) -> str:
        """
        Obtém metadados das métricas e dimensões disponíveis.
        
        Args:
            metadata_type: Tipo de metadados ("metrics", "dimensions", "all")
            property_id: ID da propriedade (opcional)
            
        Returns:
            Metadados formatados
        """
        if not self.mcp_client:
            raise RuntimeError("Cliente MCP não está iniciado")
        
        arguments = {
            "type": metadata_type
        }
        
        if property_id:
            arguments["property_id"] = property_id
        
        result = await self.mcp_client.call_tool("get_metadata", arguments)
        return result.get("content", [{}])[0].get("text", "")


# Função de conveniência para uso síncrono
def create_ga4_client(property_id: Optional[str] = None) -> GA4MCPClient:
    """
    Cria um cliente GA4 MCP.
    
    Args:
        property_id: ID da propriedade GA4
        
    Returns:
        Cliente GA4 MCP
    """
    return GA4MCPClient(property_id)
