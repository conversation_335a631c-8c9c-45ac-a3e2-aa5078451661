"""Ferramentas LangChain para integração com GA4 via MCP."""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Type, Union

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from langchain_ga4_client import GA4MCPClient

logger = logging.getLogger("langchain-ga4-tools")


class GA4ReportInput(BaseModel):
    """Input schema para relatórios GA4."""
    metrics: List[str] = Field(
        description="Lista de métricas do GA4 (ex: ['activeUsers', 'sessions'])"
    )
    dimensions: Optional[List[str]] = Field(
        default=None,
        description="Lista de dimensões do GA4 (ex: ['date', 'country'])"
    )
    date_range: Union[Dict[str, str], str] = Field(
        default="last30days",
        description="Período do relatório. Pode ser 'today', 'yesterday', 'last7days', 'last30days' ou dict com start_date e end_date"
    )
    property_id: Optional[str] = Field(
        default=None,
        description="ID da propriedade GA4 (opcional se configurado no cliente)"
    )
    limit: int = Field(
        default=10,
        description="Número máximo de linhas a retornar"
    )


class GA4RealtimeReportInput(BaseModel):
    """Input schema para relatórios em tempo real do GA4."""
    metrics: List[str] = Field(
        description="Lista de métricas do GA4 (ex: ['activeUsers', 'screenPageViews'])"
    )
    dimensions: Optional[List[str]] = Field(
        default=None,
        description="Lista de dimensões do GA4 (ex: ['country', 'city'])"
    )
    property_id: Optional[str] = Field(
        default=None,
        description="ID da propriedade GA4 (opcional se configurado no cliente)"
    )
    limit: int = Field(
        default=10,
        description="Número máximo de linhas a retornar"
    )


class GA4MetadataInput(BaseModel):
    """Input schema para metadados do GA4."""
    metadata_type: str = Field(
        default="all",
        description="Tipo de metadados: 'metrics', 'dimensions' ou 'all'"
    )
    property_id: Optional[str] = Field(
        default=None,
        description="ID da propriedade GA4 (opcional se configurado no cliente)"
    )


class GA4ReportTool(BaseTool):
    """Ferramenta para executar relatórios GA4."""

    name: str = "ga4_report"
    description: str = """
    Executa relatórios do Google Analytics 4 com métricas e dimensões especificadas.

    Use esta ferramenta para obter dados históricos do GA4, como:
    - Número de usuários ativos, sessões, visualizações de página
    - Dados por país, cidade, dispositivo, canal de aquisição
    - Dados de conversões e receita

    Métricas comuns: activeUsers, sessions, screenPageViews, conversions, totalRevenue
    Dimensões comuns: date, country, city, deviceCategory, sessionDefaultChannelGroup
    """
    args_schema: Type[BaseModel] = GA4ReportInput

    def __init__(self, ga4_client: GA4MCPClient, **kwargs):
        super().__init__(**kwargs)
        # Usar object.__setattr__ para contornar a validação do Pydantic
        object.__setattr__(self, 'ga4_client', ga4_client)
    
    def _run(
        self,
        metrics: List[str],
        dimensions: Optional[List[str]] = None,
        date_range: Union[Dict[str, str], str] = "last30days",
        property_id: Optional[str] = None,
        limit: int = 10,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Executa o relatório GA4."""
        try:
            # Executar de forma assíncrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.ga4_client.run_report(
                    metrics=metrics,
                    dimensions=dimensions,
                    date_range=date_range,
                    property_id=property_id,
                    limit=limit
                )
            )
            
            loop.close()
            return result
            
        except Exception as e:
            logger.error(f"Erro ao executar relatório GA4: {e}")
            return f"Erro ao executar relatório: {str(e)}"


class GA4RealtimeReportTool(BaseTool):
    """Ferramenta para executar relatórios em tempo real do GA4."""

    name: str = "ga4_realtime_report"
    description: str = """
    Executa relatórios em tempo real do Google Analytics 4 para os últimos 30 minutos.

    Use esta ferramenta para obter dados atuais do GA4, como:
    - Usuários ativos no momento
    - Visualizações de página em tempo real
    - Dados por localização geográfica atual

    Métricas comuns: activeUsers, screenPageViews
    Dimensões comuns: country, city, unifiedScreenName
    """
    args_schema: Type[BaseModel] = GA4RealtimeReportInput

    def __init__(self, ga4_client: GA4MCPClient, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'ga4_client', ga4_client)
    
    def _run(
        self,
        metrics: List[str],
        dimensions: Optional[List[str]] = None,
        property_id: Optional[str] = None,
        limit: int = 10,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Executa o relatório em tempo real do GA4."""
        try:
            # Executar de forma assíncrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.ga4_client.run_realtime_report(
                    metrics=metrics,
                    dimensions=dimensions,
                    property_id=property_id,
                    limit=limit
                )
            )
            
            loop.close()
            return result
            
        except Exception as e:
            logger.error(f"Erro ao executar relatório em tempo real GA4: {e}")
            return f"Erro ao executar relatório em tempo real: {str(e)}"


class GA4MetadataTool(BaseTool):
    """Ferramenta para obter metadados do GA4."""

    name: str = "ga4_metadata"
    description: str = """
    Obtém informações sobre métricas e dimensões disponíveis no Google Analytics 4.

    Use esta ferramenta para:
    - Descobrir quais métricas estão disponíveis
    - Descobrir quais dimensões estão disponíveis
    - Entender o que cada métrica/dimensão representa

    Tipos de metadados: 'metrics', 'dimensions', 'all'
    """
    args_schema: Type[BaseModel] = GA4MetadataInput

    def __init__(self, ga4_client: GA4MCPClient, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'ga4_client', ga4_client)
    
    def _run(
        self,
        metadata_type: str = "all",
        property_id: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Obtém metadados do GA4."""
        try:
            # Executar de forma assíncrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.ga4_client.get_metadata(
                    metadata_type=metadata_type,
                    property_id=property_id
                )
            )
            
            loop.close()
            return result
            
        except Exception as e:
            logger.error(f"Erro ao obter metadados GA4: {e}")
            return f"Erro ao obter metadados: {str(e)}"


def create_ga4_tools(ga4_client: GA4MCPClient) -> List[BaseTool]:
    """
    Cria todas as ferramentas GA4 para LangChain.
    
    Args:
        ga4_client: Cliente GA4 MCP
        
    Returns:
        Lista de ferramentas GA4
    """
    return [
        GA4ReportTool(ga4_client=ga4_client),
        GA4RealtimeReportTool(ga4_client=ga4_client),
        GA4MetadataTool(ga4_client=ga4_client),
    ]
