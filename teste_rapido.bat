@echo off
setlocal enabledelayedexpansion
title Teste Rapido - Agente GA4

echo.
echo Teste Rapido do Agente GA4
echo ===========================
echo.

REM Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

REM Solicitar chave OpenAI
if "%OPENAI_API_KEY%"=="" (
    echo Digite sua chave da API OpenAI:
    set /p OPENAI_API_KEY=
    echo.
)

REM Solicitar Property ID (opcional)
if "%GA4_PROPERTY_ID%"=="" (
    echo Digite o ID da propriedade GA4 (ou Enter para pular):
    set /p GA4_PROPERTY_ID=
    echo.
)

REM Executar teste
echo Executando teste...
echo.
python teste_ga4_agent.py

echo.
echo Pressione qualquer tecla para sair...
pause >nul
