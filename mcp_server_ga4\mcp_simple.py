"""
Implementação simplificada do MCP para compatibilidade.
Esta é uma versão básica que permite executar o servidor sem a dependência completa do MCP.
"""
import json
import sys
from typing import Any, Dict, List, Optional, Callable
import asyncio
import logging

logger = logging.getLogger("mcp-simple")


class Context:
    """Contexto simplificado para compatibilidade com MCP."""
    
    def __init__(self, lifespan_context: Dict[str, Any]):
        self.request_context = SimpleRequestContext(lifespan_context)


class SimpleRequestContext:
    """Contexto de requisição simplificado."""
    
    def __init__(self, lifespan_context: Dict[str, Any]):
        self.lifespan_context = lifespan_context


class SimpleMCPServer:
    """Servidor MCP simplificado para testes e desenvolvimento."""
    
    def __init__(self, name: str, **kwargs):
        self.name = name
        self.tools = {}
        self.lifespan_context = {}
        
    def tool(self):
        """Decorator para registrar ferramentas."""
        def decorator(func: Callable):
            self.tools[func.__name__] = func
            return func
        return decorator
    
    async def run_stdio(self):
        """Executa o servidor em modo stdio simplificado."""
        logger.info(f"Starting {self.name} MCP server in stdio mode")
        
        # Inicializa o contexto de vida útil
        from .ga4_client import GA4Client
        import os
        
        property_id = os.environ.get("GA4_PROPERTY_ID")
        ga4_client = GA4Client(default_property_id=property_id)
        
        try:
            await ga4_client.verify_auth()
            logger.info("GA4 authentication successful")
        except Exception as e:
            logger.warning(f"GA4 authentication warning: {e}")
        
        self.lifespan_context = {"ga4_client": ga4_client}
        
        # Loop principal de processamento
        try:
            while True:
                try:
                    # Lê entrada do stdin
                    line = await asyncio.get_event_loop().run_in_executor(
                        None, sys.stdin.readline
                    )
                    
                    if not line:
                        break
                        
                    # Processa comando
                    try:
                        request = json.loads(line.strip())
                        response = await self.process_request(request)
                        print(json.dumps(response))
                        sys.stdout.flush()
                    except json.JSONDecodeError:
                        # Ignora linhas que não são JSON válido
                        continue
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    
        finally:
            await ga4_client.close()
            
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Processa uma requisição MCP."""
        method = request.get("method", "")
        params = request.get("params", {})
        
        if method == "tools/list":
            return {
                "tools": [
                    {
                        "name": name,
                        "description": func.__doc__ or f"Tool: {name}"
                    }
                    for name, func in self.tools.items()
                ]
            }
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name in self.tools:
                try:
                    ctx = Context(self.lifespan_context)
                    result = await self.tools[tool_name](ctx, **arguments)
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": str(result)
                            }
                        ]
                    }
                except Exception as e:
                    return {
                        "error": {
                            "code": -1,
                            "message": str(e)
                        }
                    }
            else:
                return {
                    "error": {
                        "code": -2,
                        "message": f"Tool {tool_name} not found"
                    }
                }
        else:
            return {
                "error": {
                    "code": -3,
                    "message": f"Unknown method: {method}"
                }
            }
    
    async def run_sse(self, host: str = "localhost", port: int = 8000):
        """Executa o servidor em modo SSE (Server-Sent Events)."""
        logger.info(f"Starting {self.name} MCP server in SSE mode on {host}:{port}")
        
        # Para SSE, vamos criar um servidor HTTP simples
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import threading
        
        class MCPHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == "/health":
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"status": "ok"}).encode())
                else:
                    self.send_response(404)
                    self.end_headers()
                    
            def do_POST(self):
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                try:
                    request = json.loads(post_data.decode())
                    # Processa a requisição de forma síncrona para simplicidade
                    response = {"message": "SSE mode not fully implemented"}
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(response).encode())
                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": str(e)}).encode())
        
        server = HTTPServer((host, port), MCPHandler)
        server.serve_forever()
    
    def run(self, transport: str = "stdio", **kwargs):
        """Executa o servidor com o transporte especificado."""
        if transport == "stdio":
            asyncio.run(self.run_stdio())
        elif transport == "sse":
            asyncio.run(self.run_sse(**kwargs))
        else:
            raise ValueError(f"Unknown transport: {transport}")


# Alias para compatibilidade
FastMCP = SimpleMCPServer
