[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-server-ga4"
version = "0.1.0"
description = "Model Context Protocol (MCP) server for Google Analytics 4 (GA4)"
readme = "README.md"
requires-python = ">=3.9"
license = { text = "MIT" }
authors = [
    { name = "MCP Community" }
]
keywords = ["mcp", "google-analytics", "ga4", "model-context-protocol"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "mcp>=1.0.0",
    "google-analytics-data>=0.16.0",
    "python-dotenv>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "mypy",
    "pytest",
]

[project.urls]
"Homepage" = "https://github.com/harshfolio/mcp-server-ga4"
"Bug Tracker" = "https://github.com/harshfolio/mcp-server-ga4/issues"

[project.scripts]
mcp-server-ga4 = "mcp_server_ga4.main:main"

[tool.setuptools]
packages = ["mcp_server_ga4"]

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88
