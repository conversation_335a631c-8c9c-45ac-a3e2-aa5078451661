@echo off
setlocal enabledelayedexpansion
title Agente GA4 com LangChain

echo.
echo ========================================
echo    AGENTE GA4 COM LANGCHAIN
echo ========================================
echo.

REM Ativar ambiente virtual se existir
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Ativando ambiente virtual...
    call venv\Scripts\activate.bat
    echo.
)

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERRO] Python nao encontrado. Instale Python primeiro.
    echo.
    pause
    exit /b 1
)

REM Verificar credenciais GA4
if not exist "credenciais-ga4.json" (
    echo [ERRO] Arquivo credenciais-ga4.json nao encontrado
    echo        Baixe as credenciais do Google Cloud Console
    echo        e salve como 'credenciais-ga4.json'
    echo.
    pause
    exit /b 1
)

echo [OK] Credenciais GA4 encontradas
echo.

REM Configurar chave OpenAI
if "%OPENAI_API_KEY%"=="" (
    echo [CONFIG] Chave OpenAI nao configurada
    echo.
    set /p "OPENAI_API_KEY=Digite sua chave da API OpenAI: "
    echo.
    
    if "!OPENAI_API_KEY!"=="" (
        echo [ERRO] Chave OpenAI eh obrigatoria
        pause
        exit /b 1
    )
    
    echo [OK] Chave OpenAI configurada
) else (
    echo [OK] Chave OpenAI ja configurada
)

echo.

REM Configurar Property ID (opcional)
if "%GA4_PROPERTY_ID%"=="" (
    echo [CONFIG] Property ID do GA4 nao configurado
    echo.
    set /p "GA4_PROPERTY_ID=Digite o ID da propriedade GA4 (ou Enter para pular): "
    echo.
    
    if not "!GA4_PROPERTY_ID!"=="" (
        echo [OK] Property ID configurado: !GA4_PROPERTY_ID!
    ) else (
        echo [INFO] Property ID nao configurado - sera solicitado em cada consulta
    )
) else (
    echo [OK] Property ID ja configurado: %GA4_PROPERTY_ID%
)

echo.
echo ========================================
echo    CONFIGURACAO COMPLETA
echo ========================================
echo.

REM Menu principal
:MENU
echo MENU PRINCIPAL:
echo.
echo 1. Executar testes do sistema
echo 2. Agente interativo (conversa)
echo 3. Consulta rapida
echo 4. Exemplos de uso
echo 5. Sair
echo.
set /p "opcao=Escolha uma opcao (1-5): "

if "%opcao%"=="1" goto TESTES
if "%opcao%"=="2" goto INTERATIVO
if "%opcao%"=="3" goto CONSULTA
if "%opcao%"=="4" goto EXEMPLOS
if "%opcao%"=="5" goto SAIR

echo.
echo [ERRO] Opcao invalida. Tente novamente.
echo.
goto MENU

:TESTES
echo.
echo ========================================
echo    EXECUTANDO TESTES
echo ========================================
echo.
python teste_ga4_agent.py
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:INTERATIVO
echo.
echo ========================================
echo    AGENTE INTERATIVO
echo ========================================
echo.
echo Iniciando agente conversacional...
echo Digite suas perguntas sobre dados do GA4
echo Digite 'sair' para voltar ao menu
echo.
python exemplo_ga4_agent.py
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:CONSULTA
echo.
echo ========================================
echo    CONSULTA RAPIDA
echo ========================================
echo.
set /p "pergunta=Digite sua pergunta sobre dados do GA4: "
echo.

if "%pergunta%"=="" (
    echo [ERRO] Pergunta nao pode estar vazia
    echo.
    goto CONSULTA
)

echo [INFO] Processando pergunta: "%pergunta%"
echo.
echo ----------------------------------------
python -c "from ga4_agent import run_ga4_query; import os; print('RESPOSTA:'); print('=' * 50); print(run_ga4_query('%pergunta%', property_id=os.getenv('GA4_PROPERTY_ID'), openai_api_key=os.getenv('OPENAI_API_KEY'))); print('=' * 50)"
echo ----------------------------------------
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:EXEMPLOS
echo.
echo ========================================
echo    EXEMPLOS DE USO
echo ========================================
echo.
echo Executando exemplos automaticos...
python -c "
from ga4_agent import run_ga4_query
import os

perguntas = [
    'Quantos usuarios ativos tivemos ontem?',
    'Mostre-me as sessoes dos ultimos 7 dias'
]

for i, pergunta in enumerate(perguntas, 1):
    print(f'\\nExemplo {i}: {pergunta}')
    print('-' * 50)
    try:
        resposta = run_ga4_query(pergunta, property_id=os.getenv('GA4_PROPERTY_ID'), openai_api_key=os.getenv('OPENAI_API_KEY'))
        print(resposta[:200] + '...' if len(resposta) > 200 else resposta)
    except Exception as e:
        print(f'Erro: {e}')
    print('-' * 50)
"
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:SAIR
echo.
echo ========================================
echo    OBRIGADO POR USAR O AGENTE GA4!
echo ========================================
echo.
pause
exit /b 0
