@echo off
chcp 65001 >nul
title Agente GA4 com LangChain

echo.
echo 🎯 Agente GA4 com LangChain
echo ========================================
echo.

REM Ativar ambiente virtual se existir
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Ativando ambiente virtual...
    call venv\Scripts\activate.bat
    echo ✅ Ambiente virtual ativado
    echo.
) else (
    echo ⚠️  Ambiente virtual não encontrado. Usando Python global.
    echo.
)

REM Verificar se Python está disponível
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado. Instale Python primeiro.
    pause
    exit /b 1
)

REM Solicitar chave OpenAI se não estiver configurada
if "%OPENAI_API_KEY%"=="" (
    echo 🔑 Configuração da API OpenAI
    echo --------------------------------
    echo.
    set /p "OPENAI_API_KEY=Digite sua chave da API OpenAI: "
    echo.
    
    if "!OPENAI_API_KEY!"=="" (
        echo ❌ Chave OpenAI é obrigatória.
        pause
        exit /b 1
    )
    
    echo ✅ Chave OpenAI configurada!
    echo.
) else (
    echo ✅ Chave OpenAI já configurada
    echo.
)

REM Solicitar Property ID se não estiver configurado
if "%GA4_PROPERTY_ID%"=="" (
    echo 📊 Configuração do GA4 (Opcional)
    echo ----------------------------------
    echo.
    set /p "GA4_PROPERTY_ID=Digite o ID da propriedade GA4 (ou Enter para pular): "
    echo.
    
    if not "!GA4_PROPERTY_ID!"=="" (
        echo ✅ Property ID configurado: !GA4_PROPERTY_ID!
    ) else (
        echo ⚠️  Property ID não configurado. Será necessário especificar em cada consulta.
    )
    echo.
) else (
    echo ✅ Property ID já configurado: %GA4_PROPERTY_ID%
    echo.
)

REM Verificar arquivo de credenciais
if not exist "credenciais-ga4.json" (
    echo ❌ Arquivo credenciais-ga4.json não encontrado!
    echo.
    echo 📁 Para usar o agente, você precisa:
    echo    1. Baixar as credenciais do Google Cloud Console
    echo    2. Salvar como 'credenciais-ga4.json' neste diretório
    echo.
    echo 🌐 Instruções: https://cloud.google.com/docs/authentication/getting-started
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Arquivo de credenciais encontrado
    echo.
)

REM Exportar variáveis de ambiente
set "OPENAI_API_KEY=%OPENAI_API_KEY%"
set "GA4_PROPERTY_ID=%GA4_PROPERTY_ID%"

REM Menu principal
:MENU
echo.
echo 🚀 Menu Principal
echo ================
echo 1. Executar testes do sistema
echo 2. Usar agente interativo
echo 3. Executar exemplos
echo 4. Configurar novamente
echo 5. Sair
echo.
set /p "opcao=Escolha uma opção (1-5): "

if "%opcao%"=="1" goto TESTES
if "%opcao%"=="2" goto INTERATIVO
if "%opcao%"=="3" goto EXEMPLOS
if "%opcao%"=="4" goto RECONFIGURAR
if "%opcao%"=="5" goto SAIR
echo ❌ Opção inválida. Tente novamente.
goto MENU

:TESTES
echo.
echo 🧪 Executando testes do sistema...
echo ==================================
python teste_ga4_agent.py
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:INTERATIVO
echo.
echo 💬 Iniciando agente interativo...
echo =================================
echo Digite suas perguntas sobre dados do GA4.
echo Digite 'menu' para voltar ao menu principal.
echo.
python exemplo_ga4_agent.py
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:EXEMPLOS
echo.
echo 📊 Executando exemplos...
echo =========================
echo.
echo Exemplo 1: Consulta simples
python -c "
from ga4_agent import run_ga4_query
import os
try:
    resposta = run_ga4_query(
        'Quantos usuários ativos tivemos ontem?',
        property_id=os.getenv('GA4_PROPERTY_ID'),
        openai_api_key=os.getenv('OPENAI_API_KEY')
    )
    print('🤖 Resposta:', resposta)
except Exception as e:
    print('❌ Erro:', e)
"
echo.
echo Pressione qualquer tecla para voltar ao menu...
pause >nul
goto MENU

:RECONFIGURAR
echo.
echo 🔧 Reconfigurando...
echo ===================
set "OPENAI_API_KEY="
set "GA4_PROPERTY_ID="
goto :eof

:SAIR
echo.
echo 👋 Obrigado por usar o Agente GA4!
echo.
pause
exit /b 0
