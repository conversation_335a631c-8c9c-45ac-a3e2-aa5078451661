# 🌐 Interface Web - Agente GA4 com Streamlit

Uma interface web moderna e intuitiva para interagir com seus dados do Google Analytics 4 usando inteligência artificial.

## 🚀 Como Executar

### Método 1: Arquivo .BAT (<PERSON><PERSON>)
```bash
# Duplo clique em:
executar_streamlit.bat
```

### Método 2: <PERSON><PERSON> Comando
```bash
# No terminal:
streamlit run app_streamlit.py
```

### Método 3: PowerShell
```powershell
# No PowerShell:
python -m streamlit run app_streamlit.py
```

## 🎯 Funcionalidades

### 💬 Chat Inteligente
- **Conversa natural** com seus dados do GA4
- **Histórico de perguntas** e respostas
- **Interface limpa** e fácil de usar

### 📊 Exemplos Prontos
- **Usuários ativos** por período
- **Análise geográfica** de visitantes
- **Distribuição por dispositivos**
- **Páginas mais populares**
- **Dados em tempo real**
- **Taxa de conversão**

### 📈 Análises Avançadas
- **Dashboard completo** da audiência
- **Análise de canais** de aquisição
- **Comportamento mobile** vs desktop
- **Insights de negócio** automatizados

### ⚙️ Configuração Fácil
- **Interface visual** para configurar chaves
- **Verificação automática** de configuração
- **Teste de conectividade** integrado

## 🔧 Configuração

### 1. Chave OpenAI
- Acesse: https://platform.openai.com/api-keys
- Crie uma nova chave secreta
- Cole na barra lateral da interface

### 2. Property ID GA4 (Opcional)
- Acesse Google Analytics 4
- Vá em Admin > Informações da propriedade
- Copie o ID da propriedade
- Cole na barra lateral

### 3. Credenciais GA4
- Baixe do Google Cloud Console
- Salve como `credenciais-ga4.json`
- Coloque no mesmo diretório da aplicação

## 💡 Como Usar

### Primeira Vez
1. **Execute** `executar_streamlit.bat`
2. **Aguarde** a interface abrir no navegador
3. **Configure** suas chaves na barra lateral
4. **Teste** a configuração
5. **Comece** a fazer perguntas!

### Uso Diário
1. **Abra** a interface web
2. **Vá na aba Chat** para perguntas livres
3. **Use Exemplos** para consultas rápidas
4. **Explore Análises** para insights profundos

## 🎨 Interface

### 📱 Layout Responsivo
- **Barra lateral** para configuração
- **Área principal** com abas organizadas
- **Design limpo** e profissional

### 🎨 Recursos Visuais
- **Cores personalizadas** para melhor experiência
- **Ícones intuitivos** para cada funcionalidade
- **Mensagens destacadas** para feedback claro

### 💬 Chat Inteligente
- **Histórico persistente** durante a sessão
- **Formatação clara** de perguntas e respostas
- **Interface similar** a aplicativos de chat

## 📊 Exemplos de Perguntas

### 👥 Análise de Audiência
- "Quantos usuários ativos tivemos nos últimos 7 dias?"
- "Compare os usuários desta semana com a semana passada"
- "Qual é a taxa de crescimento mensal de usuários?"

### 🌍 Análise Geográfica
- "Mostre-me os usuários por país dos últimos 30 dias"
- "Quais cidades brasileiras têm mais visitantes?"
- "Compare o tráfego do Brasil vs outros países"

### 📱 Análise de Dispositivos
- "Qual é a distribuição de usuários por dispositivo?"
- "Como é o comportamento dos usuários mobile?"
- "Desktop ou mobile tem melhor taxa de conversão?"

### 📈 Análise de Conteúdo
- "Quais são as páginas mais visitadas esta semana?"
- "Qual página tem maior tempo de permanência?"
- "Quais páginas têm maior taxa de rejeição?"

### ⏱️ Dados em Tempo Real
- "Quantos usuários estão online agora?"
- "Quais páginas estão sendo visitadas neste momento?"
- "De quais países vêm os usuários ativos agora?"

### 💰 Análise de Conversões
- "Qual é a taxa de conversão dos últimos 30 dias?"
- "Quantas conversões tivemos esta semana?"
- "Qual canal de aquisição gera mais conversões?"

## 🔧 Personalização

### 🎨 Temas
- Edite `.streamlit/config.toml` para personalizar cores
- Modifique CSS em `app_streamlit.py` para layout

### 📊 Exemplos
- Adicione novos exemplos no array `examples`
- Crie análises personalizadas no array `analyses`

### 💬 Chat
- Personalize mensagens de sistema
- Adicione validações específicas

## 🐛 Solução de Problemas

### ❌ "Streamlit não encontrado"
```bash
pip install streamlit plotly pandas
```

### ❌ Interface não abre
- Verifique se a porta 8501 está livre
- Acesse manualmente: http://localhost:8501

### ❌ "Erro de configuração"
- Verifique chave OpenAI na barra lateral
- Confirme se credenciais-ga4.json existe

### ❌ "Erro ao processar consulta"
- Teste a configuração usando o botão de teste
- Verifique se há créditos na conta OpenAI

## 🚀 Vantagens da Interface Web

### ✅ Facilidade de Uso
- **Sem linha de comando** necessária
- **Interface visual** intuitiva
- **Configuração guiada** passo a passo

### ✅ Experiência Rica
- **Chat interativo** com histórico
- **Exemplos prontos** para começar rapidamente
- **Análises avançadas** com um clique

### ✅ Acessibilidade
- **Funciona em qualquer navegador**
- **Responsivo** para diferentes telas
- **Fácil de compartilhar** com equipe

## 🎉 Pronto para Usar!

Execute `executar_streamlit.bat` e comece a explorar seus dados do GA4 de forma inteligente e visual!

**🌐 Acesse: http://localhost:8501**
