@echo off
setlocal enabledelayedexpansion
title Agente GA4 com Lang<PERSON>hain

echo.
echo Agente GA4 com LangChain
echo ========================
echo.

REM Ativar ambiente virtual se existir
if exist "venv\Scripts\activate.bat" (
    echo Ativando ambiente virtual...
    call venv\Scripts\activate.bat
    echo.
)

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado
    pause
    exit /b 1
)

REM Solicitar chave OpenAI
if "%OPENAI_API_KEY%"=="" (
    echo Digite sua chave da API OpenAI:
    set /p OPENAI_API_KEY=
    echo.
)

REM Solicitar Property ID (opcional)
if "%GA4_PROPERTY_ID%"=="" (
    echo Digite o ID da propriedade GA4 (opcional):
    set /p GA4_PROPERTY_ID=
    echo.
)

REM Verificar credenciais
if not exist "credenciais-ga4.json" (
    echo ERRO: Arquivo credenciais-ga4.json nao encontrado
    echo Baixe as credenciais do Google Cloud Console
    pause
    exit /b 1
)

REM Menu
:MENU
echo.
echo Menu Principal:
echo 1. Executar testes
echo 2. Agente interativo
echo 3. Consulta rapida
echo 4. Sair
echo.
set /p opcao=Escolha (1-4): 

if "%opcao%"=="1" goto TESTES
if "%opcao%"=="2" goto INTERATIVO
if "%opcao%"=="3" goto CONSULTA
if "%opcao%"=="4" goto SAIR
echo Opcao invalida
goto MENU

:TESTES
echo.
echo Executando testes...
python teste_ga4_agent.py
pause
goto MENU

:INTERATIVO
echo.
echo Iniciando agente interativo...
python exemplo_ga4_agent.py
pause
goto MENU

:CONSULTA
echo.
echo Digite sua pergunta sobre dados do GA4:
set /p pergunta=
echo.
echo Processando...
python -c "from ga4_agent import run_ga4_query; import os; print(run_ga4_query('%pergunta%', property_id=os.getenv('GA4_PROPERTY_ID'), openai_api_key=os.getenv('OPENAI_API_KEY')))"
pause
goto MENU

:SAIR
echo.
echo Obrigado por usar o Agente GA4!
pause
exit /b 0
