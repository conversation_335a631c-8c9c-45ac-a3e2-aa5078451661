# 🚀 Como Usar os Arquivos .BAT

Criei 3 arquivos .bat para facilitar o uso do Agente GA4:

## 📁 Arquivos Disponíveis

### 1. `executar_agente_ga4.bat` - **Principal**
- **Uso**: Interface completa com menu
- **Funcionalidades**:
  - Configuração automática de chaves
  - Menu com opções de teste e uso
  - Verificação de dependências
  - Modo interativo completo

### 2. `teste_rapido.bat` - **Teste**
- **Uso**: Teste rápido do sistema
- **Funcionalidades**:
  - Solicita chave OpenAI
  - Executa todos os testes
  - Verifica se tudo está funcionando

### 3. `consulta_ga4.bat` - **Consulta Direta**
- **Uso**: Fazer uma pergunta específica
- **Funcionalidades**:
  - Pergunta direta ao agente
  - Resposta imediata
  - Ideal para consultas rápidas

## 🎯 Como Usar

### Primeira Vez (Recomendado)

1. **Execute o teste rápido**:
   ```
   Duplo clique em: teste_rapido.bat
   ```
   - Digite sua chave OpenAI quando solicitado
   - Digite seu Property ID do GA4 (opcional)
   - Aguarde os testes terminarem

2. **Se os testes passaram, use o agente principal**:
   ```
   Duplo clique em: executar_agente_ga4.bat
   ```

### Uso Diário

**Para consultas rápidas**:
```
Duplo clique em: consulta_ga4.bat
```
- Digite sua pergunta
- Receba a resposta

**Para uso completo**:
```
Duplo clique em: executar_agente_ga4.bat
```
- Escolha opção 2 (agente interativo)

## 🔑 Informações Necessárias

### Chave OpenAI (Obrigatória)
- Formato: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- Obtenha em: https://platform.openai.com/api-keys

### Property ID do GA4 (Opcional)
- Formato: `123456789`
- Encontre no Google Analytics 4 > Admin > Informações da propriedade

### Credenciais GA4 (Obrigatória)
- Arquivo: `credenciais-ga4.json`
- Baixe do Google Cloud Console
- Coloque no mesmo diretório dos arquivos .bat

## 💬 Exemplos de Perguntas

Quando usar o agente, experimente perguntas como:

- "Quantos usuários ativos tivemos ontem?"
- "Mostre-me os usuários por país dos últimos 30 dias"
- "Quais são as páginas mais visitadas esta semana?"
- "Quantos usuários estão online agora?"
- "Qual é a taxa de conversão atual?"
- "Faça uma análise completa da audiência dos últimos 30 dias"

## 🐛 Solução de Problemas

### ❌ "Python não encontrado"
- Instale Python 3.9+ de python.org
- Certifique-se que está no PATH

### ❌ "Ambiente virtual não encontrado"
- Normal se não tiver venv
- O script usará Python global

### ❌ "credenciais-ga4.json não encontrado"
- Baixe as credenciais do Google Cloud Console
- Salve como `credenciais-ga4.json` no diretório

### ❌ "Erro no servidor MCP"
- Execute: `pip install -e .` no diretório do projeto
- Verifique se todas as dependências estão instaladas

### ❌ "Chave OpenAI inválida"
- Verifique se a chave está correta
- Confirme se há créditos na conta OpenAI

## 🎉 Pronto!

Agora você pode usar o Agente GA4 facilmente:

1. **Teste primeiro**: `teste_rapido.bat`
2. **Use diariamente**: `consulta_ga4.bat` ou `executar_agente_ga4.bat`
3. **Faça perguntas em português** sobre seus dados do GA4!

## 📞 Suporte

Se tiver problemas:
1. Execute `teste_rapido.bat` para diagnóstico
2. Verifique se todas as configurações estão corretas
3. Consulte os logs de erro para mais detalhes
