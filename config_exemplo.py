"""Arquivo de configuração de exemplo para o agente GA4."""
import os
from typing import Optional


class GA4Config:
    """Configuração para o agente GA4."""
    
    def __init__(self):
        """Inicializa a configuração com valores padrão."""
        # Configurações do GA4
        self.property_id: Optional[str] = os.getenv("GA4_PROPERTY_ID")
        self.credentials_file: str = "credenciais-ga4.json"
        
        # Configurações do OpenAI
        self.openai_api_key: Optional[str] = os.getenv("OPENAI_API_KEY")
        self.model_name: str = "gpt-4"
        self.temperature: float = 0.1
        
        # Configurações do agente
        self.max_iterations: int = 5
        self.verbose: bool = True
        
        # Configurações de relatórios padrão
        self.default_limit: int = 10
        self.default_date_range: str = "last30days"
    
    def validate(self) -> bool:
        """
        Valida se a configuração está correta.
        
        Returns:
            True se válida, False caso contrário
        """
        errors = []
        
        # Verificar chave OpenAI
        if not self.openai_api_key:
            errors.append("OPENAI_API_KEY não configurada")
        
        # Verificar arquivo de credenciais
        if not os.path.exists(self.credentials_file):
            errors.append(f"Arquivo de credenciais não encontrado: {self.credentials_file}")
        
        # Verificar property_id (opcional, mas recomendado)
        if not self.property_id:
            print("⚠️  GA4_PROPERTY_ID não configurado. Será necessário especificar em cada consulta.")
        
        if errors:
            print("❌ Erros de configuração:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        print("✅ Configuração válida!")
        return True
    
    def print_config(self):
        """Imprime a configuração atual."""
        print("📋 Configuração Atual:")
        print(f"  Property ID: {self.property_id or 'Não configurado'}")
        print(f"  Credenciais: {self.credentials_file}")
        print(f"  OpenAI Key: {'Configurada' if self.openai_api_key else 'Não configurada'}")
        print(f"  Modelo: {self.model_name}")
        print(f"  Temperature: {self.temperature}")
        print(f"  Limite padrão: {self.default_limit}")
        print(f"  Período padrão: {self.default_date_range}")


# Configuração global
config = GA4Config()


def setup_environment():
    """Configura o ambiente para o agente GA4."""
    print("🔧 Configurando ambiente do Agente GA4...")
    
    # Verificar e solicitar configurações se necessário
    if not config.openai_api_key:
        print("\n🔑 Configuração da API OpenAI:")
        api_key = input("Digite sua chave da API OpenAI: ").strip()
        if api_key:
            os.environ["OPENAI_API_KEY"] = api_key
            config.openai_api_key = api_key
            print("✅ Chave OpenAI configurada!")
    
    if not config.property_id:
        print("\n📊 Configuração do GA4:")
        property_id = input("Digite o ID da propriedade GA4 (opcional): ").strip()
        if property_id:
            os.environ["GA4_PROPERTY_ID"] = property_id
            config.property_id = property_id
            print("✅ Property ID configurado!")
    
    # Verificar arquivo de credenciais
    if not os.path.exists(config.credentials_file):
        print(f"\n📁 Arquivo de credenciais não encontrado: {config.credentials_file}")
        print("Por favor, coloque o arquivo de credenciais do GA4 no diretório atual.")
        print("O arquivo deve ser baixado do Google Cloud Console.")
    
    # Validar configuração final
    print("\n🔍 Validando configuração...")
    return config.validate()


def get_sample_queries():
    """Retorna consultas de exemplo para testar o agente."""
    return [
        # Consultas básicas
        "Quantos usuários ativos tivemos ontem?",
        "Mostre-me as sessões dos últimos 7 dias",
        "Qual é o número de visualizações de página desta semana?",
        
        # Consultas por dimensão
        "Usuários por país dos últimos 30 dias",
        "Sessões por dispositivo (desktop, mobile, tablet) esta semana",
        "Páginas mais visitadas dos últimos 7 dias",
        
        # Consultas de conversão
        "Quantas conversões tivemos este mês?",
        "Qual é a receita total dos últimos 30 dias?",
        "Taxa de conversão por canal de aquisição",
        
        # Consultas em tempo real
        "Quantos usuários estão online agora?",
        "Quais páginas estão sendo visitadas neste momento?",
        "Usuários ativos por país em tempo real",
        
        # Consultas analíticas
        "Faça uma análise completa da audiência dos últimos 30 dias",
        "Compare o desempenho desta semana com a semana passada",
        "Quais são os principais insights dos dados de hoje?",
    ]


def print_sample_queries():
    """Imprime consultas de exemplo."""
    print("\n💡 Consultas de Exemplo:")
    print("=" * 50)
    
    queries = get_sample_queries()
    for i, query in enumerate(queries, 1):
        print(f"{i:2d}. {query}")
    
    print("\n💭 Dicas:")
    print("- Seja específico sobre o período (ontem, última semana, etc.)")
    print("- Mencione as dimensões que quer analisar (país, dispositivo, etc.)")
    print("- Peça análises e insights, não apenas números")
    print("- Use linguagem natural - o agente entende português!")


if __name__ == "__main__":
    print("🎯 Configuração do Agente GA4 com LangChain")
    print("=" * 50)
    
    # Mostrar configuração atual
    config.print_config()
    
    # Configurar ambiente se necessário
    if not config.validate():
        print("\n🔧 Configuração necessária...")
        if setup_environment():
            print("\n✅ Ambiente configurado com sucesso!")
        else:
            print("\n❌ Falha na configuração do ambiente.")
    
    # Mostrar consultas de exemplo
    print_sample_queries()
    
    print("\n🚀 Para começar a usar:")
    print("python exemplo_ga4_agent.py")
