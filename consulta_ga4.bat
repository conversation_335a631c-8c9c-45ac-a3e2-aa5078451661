@echo off
chcp 65001 >nul
title Consulta GA4

echo.
echo 💬 Consulta Direta ao GA4
echo =========================
echo.

REM Ativar ambiente virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

REM Solicitar configurações se não estiverem definidas
if "%OPENAI_API_KEY%"=="" (
    echo 🔑 Digite sua chave da API OpenAI:
    set /p "OPENAI_API_KEY="
    echo.
)

if "%GA4_PROPERTY_ID%"=="" (
    echo 📊 Digite o ID da propriedade GA4 (opcional):
    set /p "GA4_PROPERTY_ID="
    echo.
)

REM Solicitar pergunta
echo 💭 Digite sua pergunta sobre os dados do GA4:
set /p "PERGUNTA="
echo.

if "%PERGUNTA%"=="" (
    echo ❌ Pergunta não pode estar vazia.
    pause
    exit /b 1
)

echo 🤖 Processando pergunta: "%PERGUNTA%"
echo.

REM Executar consulta
python -c "
from ga4_agent import run_ga4_query
import os
import sys

try:
    pergunta = '''%PERGUNTA%'''
    property_id = os.getenv('GA4_PROPERTY_ID') if os.getenv('GA4_PROPERTY_ID') else None
    openai_key = os.getenv('OPENAI_API_KEY')
    
    print('🔍 Consultando dados do GA4...')
    print()
    
    resposta = run_ga4_query(
        pergunta,
        property_id=property_id,
        openai_api_key=openai_key
    )
    
    print('🤖 Resposta do Agente:')
    print('=' * 50)
    print(resposta)
    print('=' * 50)
    
except Exception as e:
    print('❌ Erro:', str(e))
    sys.exit(1)
"

echo.
echo ✅ Consulta concluída!
echo.
echo Pressione qualquer tecla para sair...
pause >nul
