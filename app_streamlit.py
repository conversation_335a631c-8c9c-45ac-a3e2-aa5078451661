"""Interface Streamlit para o Agente GA4 com LangChain."""
import streamlit as st
import asyncio
import os
import time
from datetime import datetime
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# Configurar página
st.set_page_config(
    page_title="Agente GA4 - Analytics Inteligente",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .success-box {
        background-color: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #ffebee;
        border: 1px solid #f44336;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Função para inicializar o estado da sessão
def init_session_state():
    """Inicializa variáveis de estado da sessão."""
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'agent_initialized' not in st.session_state:
        st.session_state.agent_initialized = False
    if 'openai_key' not in st.session_state:
        st.session_state.openai_key = ""
    if 'property_id' not in st.session_state:
        st.session_state.property_id = ""

# Função para verificar configuração
def check_configuration():
    """Verifica se a configuração está completa."""
    openai_key = st.session_state.openai_key or os.getenv("OPENAI_API_KEY")
    credentials_exist = os.path.exists("credenciais-ga4.json")
    
    return {
        'openai_configured': bool(openai_key),
        'credentials_exist': credentials_exist,
        'all_configured': bool(openai_key) and credentials_exist
    }

# Função para executar consulta
def run_query(question, openai_key, property_id=None):
    """Executa uma consulta no agente GA4."""
    try:
        # Importar aqui para evitar problemas de inicialização
        from ga4_agent import run_ga4_query
        
        # Configurar variáveis de ambiente temporariamente
        original_openai = os.getenv("OPENAI_API_KEY")
        original_property = os.getenv("GA4_PROPERTY_ID")
        
        os.environ["OPENAI_API_KEY"] = openai_key
        if property_id:
            os.environ["GA4_PROPERTY_ID"] = property_id
        
        # Executar consulta
        response = run_ga4_query(
            query=question,
            property_id=property_id,
            openai_api_key=openai_key
        )
        
        # Restaurar variáveis originais
        if original_openai:
            os.environ["OPENAI_API_KEY"] = original_openai
        if original_property:
            os.environ["GA4_PROPERTY_ID"] = original_property
            
        return response
        
    except Exception as e:
        return f"Erro ao processar consulta: {str(e)}"

# Função principal
def main():
    """Função principal da aplicação."""
    init_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">📊 Agente GA4 - Analytics Inteligente</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar para configuração
    with st.sidebar:
        st.header("⚙️ Configuração")
        
        # Configuração OpenAI
        st.subheader("🔑 OpenAI API")
        openai_key = st.text_input(
            "Chave da API OpenAI",
            type="password",
            value=st.session_state.openai_key,
            help="Sua chave da API OpenAI (sk-...)"
        )
        if openai_key:
            st.session_state.openai_key = openai_key
        
        # Configuração GA4
        st.subheader("📊 Google Analytics 4")
        property_id = st.text_input(
            "Property ID (opcional)",
            value=st.session_state.property_id,
            help="ID da propriedade GA4 (ex: 123456789)"
        )
        if property_id:
            st.session_state.property_id = property_id
        
        # Status da configuração
        config = check_configuration()
        st.subheader("📋 Status")
        
        if config['openai_configured']:
            st.success("✅ OpenAI configurada")
        else:
            st.error("❌ OpenAI não configurada")
        
        if config['credentials_exist']:
            st.success("✅ Credenciais GA4 encontradas")
        else:
            st.error("❌ credenciais-ga4.json não encontrado")
        
        if st.session_state.property_id:
            st.info(f"📊 Property ID: {st.session_state.property_id}")
        else:
            st.warning("⚠️ Property ID não configurado")
        
        # Botão de teste
        if st.button("🧪 Testar Configuração"):
            if config['all_configured']:
                with st.spinner("Testando..."):
                    try:
                        response = run_query(
                            "Teste de conexão",
                            st.session_state.openai_key,
                            st.session_state.property_id
                        )
                        if "erro" not in response.lower():
                            st.success("✅ Configuração funcionando!")
                        else:
                            st.error(f"❌ Erro no teste: {response}")
                    except Exception as e:
                        st.error(f"❌ Erro no teste: {e}")
            else:
                st.error("❌ Configure OpenAI e credenciais GA4 primeiro")
    
    # Área principal
    if not check_configuration()['all_configured']:
        st.warning("⚠️ Configure a chave OpenAI e credenciais GA4 na barra lateral para começar.")
        
        # Instruções de configuração
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🔑 Como obter chave OpenAI")
            st.markdown("""
            1. Acesse [platform.openai.com](https://platform.openai.com/api-keys)
            2. Faça login na sua conta
            3. Clique em "Create new secret key"
            4. Copie a chave (começa com `sk-`)
            5. Cole na barra lateral
            """)
        
        with col2:
            st.subheader("📊 Como obter Property ID")
            st.markdown("""
            1. Acesse [Google Analytics 4](https://analytics.google.com/)
            2. Vá em Admin > Informações da propriedade
            3. Copie o ID da propriedade (número)
            4. Cole na barra lateral (opcional)
            """)
        
        st.subheader("📁 Credenciais GA4")
        st.markdown("""
        - Baixe as credenciais do Google Cloud Console
        - Salve como `credenciais-ga4.json` no diretório da aplicação
        - [Instruções detalhadas](https://cloud.google.com/docs/authentication/getting-started)
        """)
        
        return
    
    # Interface principal - Tabs
    tab1, tab2, tab3 = st.tabs(["💬 Chat", "📊 Exemplos", "📈 Análises"])
    
    with tab1:
        st.subheader("💬 Converse com seus dados do GA4")
        
        # Área de chat
        chat_container = st.container()
        
        # Exibir histórico do chat
        with chat_container:
            for message in st.session_state.chat_history:
                if message['role'] == 'user':
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>🧑 Você:</strong> {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="chat-message assistant-message">
                        <strong>🤖 Agente:</strong> {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
        
        # Input para nova pergunta
        with st.form("chat_form", clear_on_submit=True):
            question = st.text_area(
                "Faça uma pergunta sobre seus dados do GA4:",
                placeholder="Ex: Quantos usuários ativos tivemos nos últimos 7 dias?",
                height=100
            )
            submitted = st.form_submit_button("🚀 Enviar")
        
        if submitted and question:
            # Adicionar pergunta ao histórico
            st.session_state.chat_history.append({
                'role': 'user',
                'content': question,
                'timestamp': datetime.now()
            })
            
            # Processar resposta
            with st.spinner("🤖 Analisando seus dados..."):
                response = run_query(
                    question,
                    st.session_state.openai_key,
                    st.session_state.property_id
                )
            
            # Adicionar resposta ao histórico
            st.session_state.chat_history.append({
                'role': 'assistant',
                'content': response,
                'timestamp': datetime.now()
            })
            
            # Recarregar para mostrar nova mensagem
            st.rerun()
    
    with tab2:
        st.subheader("📊 Exemplos de Consultas")
        
        # Exemplos predefinidos
        examples = [
            {
                "title": "👥 Usuários Ativos",
                "question": "Quantos usuários ativos tivemos nos últimos 7 dias?",
                "description": "Mostra o número de usuários únicos que visitaram seu site/app"
            },
            {
                "title": "🌍 Análise Geográfica",
                "question": "Mostre-me os usuários por país dos últimos 30 dias",
                "description": "Distribução geográfica dos seus usuários"
            },
            {
                "title": "📱 Dispositivos",
                "question": "Qual é a distribuição de usuários por tipo de dispositivo?",
                "description": "Desktop vs Mobile vs Tablet"
            },
            {
                "title": "📈 Páginas Populares",
                "question": "Quais são as páginas mais visitadas esta semana?",
                "description": "Top páginas por visualizações"
            },
            {
                "title": "⏱️ Tempo Real",
                "question": "Quantos usuários estão online agora?",
                "description": "Dados em tempo real dos últimos 30 minutos"
            },
            {
                "title": "💰 Conversões",
                "question": "Qual é a taxa de conversão dos últimos 30 dias?",
                "description": "Performance de conversões e objetivos"
            }
        ]
        
        # Exibir exemplos em grid
        cols = st.columns(2)
        for i, example in enumerate(examples):
            with cols[i % 2]:
                with st.expander(f"{example['title']}", expanded=False):
                    st.write(example['description'])
                    st.code(example['question'])
                    
                    if st.button(f"🚀 Executar", key=f"example_{i}"):
                        with st.spinner("Processando..."):
                            response = run_query(
                                example['question'],
                                st.session_state.openai_key,
                                st.session_state.property_id
                            )
                        
                        st.success("✅ Resposta:")
                        st.write(response)
    
    with tab3:
        st.subheader("📈 Análises Avançadas")
        
        # Análises predefinidas
        analyses = [
            {
                "title": "📊 Dashboard Completo",
                "question": "Faça uma análise completa da audiência dos últimos 30 dias incluindo usuários, sessões, páginas mais visitadas e dados demográficos",
                "icon": "📊"
            },
            {
                "title": "🎯 Análise de Canais",
                "question": "Analise o desempenho dos canais de aquisição: qual canal traz mais usuários e qual tem melhor taxa de conversão?",
                "icon": "🎯"
            },
            {
                "title": "📱 Comportamento Mobile",
                "question": "Compare o comportamento dos usuários mobile vs desktop: tempo de sessão, páginas por sessão e taxa de rejeição",
                "icon": "📱"
            },
            {
                "title": "💡 Insights de Negócio",
                "question": "Quais são os principais insights dos dados dos últimos 30 dias que podem ajudar a melhorar o negócio?",
                "icon": "💡"
            }
        ]
        
        for analysis in analyses:
            with st.expander(f"{analysis['icon']} {analysis['title']}", expanded=False):
                st.write(f"**Pergunta:** {analysis['question']}")
                
                if st.button(f"🚀 Executar Análise", key=f"analysis_{analysis['title']}"):
                    with st.spinner("🔍 Analisando dados..."):
                        response = run_query(
                            analysis['question'],
                            st.session_state.openai_key,
                            st.session_state.property_id
                        )
                    
                    st.success("✅ Análise Completa:")
                    st.markdown(response)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        🚀 Agente GA4 com LangChain | Desenvolvido para análise inteligente de dados
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
