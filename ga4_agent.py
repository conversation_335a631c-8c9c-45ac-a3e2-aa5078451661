"""Agente conversacional para análise de dados do Google Analytics 4."""
import asyncio
import logging
import os
from typing import Any, Dict, List, Optional

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_openai import ChatOpenAI

from langchain_ga4_client import GA4MCPClient
from langchain_ga4_tools import create_ga4_tools

logger = logging.getLogger("ga4-agent")


class GA4Agent:
    """Agente conversacional para análise de dados do GA4."""
    
    def __init__(
        self,
        property_id: Optional[str] = None,
        openai_api_key: Optional[str] = None,
        model_name: str = "gpt-4",
        temperature: float = 0.1
    ):
        """
        Inicializa o agente GA4.
        
        Args:
            property_id: ID da propriedade GA4
            openai_api_key: Chave da API OpenAI
            model_name: Nome do modelo a usar
            temperature: Temperatura para geração de texto
        """
        self.property_id = property_id
        self.ga4_client = None
        self.agent_executor = None
        
        # Configurar OpenAI
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY deve ser fornecida")
        
        self.llm = ChatOpenAI(
            api_key=api_key,
            model=model_name,
            temperature=temperature
        )
        
    async def start(self):
        """Inicia o agente e suas dependências."""
        try:
            # Inicializar cliente GA4 MCP
            self.ga4_client = GA4MCPClient(self.property_id)
            await self.ga4_client.start()
            
            # Criar ferramentas
            tools = create_ga4_tools(self.ga4_client)
            
            # Criar prompt do agente
            prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content=self._get_system_prompt()),
                MessagesPlaceholder(variable_name="chat_history", optional=True),
                HumanMessage(content="{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad")
            ])
            
            # Criar agente
            agent = create_tool_calling_agent(self.llm, tools, prompt)
            
            # Criar executor do agente
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=5
            )
            
            logger.info("Agente GA4 iniciado com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao iniciar agente GA4: {e}")
            raise
    
    async def stop(self):
        """Para o agente e suas dependências."""
        if self.ga4_client:
            await self.ga4_client.stop()
        logger.info("Agente GA4 parado")
    
    async def chat(self, message: str, chat_history: Optional[List] = None) -> str:
        """
        Processa uma mensagem do usuário e retorna a resposta.
        
        Args:
            message: Mensagem do usuário
            chat_history: Histórico da conversa (opcional)
            
        Returns:
            Resposta do agente
        """
        if not self.agent_executor:
            raise RuntimeError("Agente não foi iniciado. Chame start() primeiro.")
        
        try:
            # Executar agente
            result = await self.agent_executor.ainvoke({
                "input": message,
                "chat_history": chat_history or []
            })
            
            return result["output"]
            
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {e}")
            return f"Desculpe, ocorreu um erro ao processar sua solicitação: {str(e)}"
    
    def _get_system_prompt(self) -> str:
        """Retorna o prompt do sistema para o agente."""
        return """Você é um assistente especializado em análise de dados do Google Analytics 4 (GA4).

Suas responsabilidades:
1. Ajudar usuários a analisar dados do GA4 através de relatórios e métricas
2. Explicar insights e tendências nos dados
3. Sugerir métricas e dimensões relevantes para diferentes tipos de análise
4. Fornecer interpretações claras e acionáveis dos dados

Ferramentas disponíveis:
- ga4_report: Para relatórios históricos com métricas e dimensões
- ga4_realtime_report: Para dados em tempo real (últimos 30 minutos)
- ga4_metadata: Para descobrir métricas e dimensões disponíveis

Diretrizes:
- Sempre explique o que as métricas significam em termos de negócio
- Sugira análises complementares quando apropriado
- Se não souber quais métricas usar, consulte os metadados primeiro
- Formate os dados de forma clara e legível
- Destaque insights importantes e tendências
- Seja proativo em sugerir próximos passos para análise

Métricas comuns e seus significados:
- activeUsers: Usuários únicos que visitaram o site/app
- sessions: Sessões de usuários (visitas)
- screenPageViews: Visualizações de página/tela
- conversions: Conversões/objetivos atingidos
- totalRevenue: Receita total gerada
- bounceRate: Taxa de rejeição
- sessionDuration: Duração média da sessão

Dimensões comuns:
- date: Data
- country/city: Localização geográfica
- deviceCategory: Tipo de dispositivo (desktop, mobile, tablet)
- sessionDefaultChannelGroup: Canal de aquisição
- pagePath: Caminho da página
- eventName: Nome do evento

Sempre forneça contexto e interpretação dos dados, não apenas números brutos."""

    async def get_available_metrics(self) -> str:
        """Obtém lista de métricas disponíveis."""
        if not self.agent_executor:
            raise RuntimeError("Agente não foi iniciado")
        
        return await self.chat("Quais métricas estão disponíveis no GA4?")
    
    async def get_available_dimensions(self) -> str:
        """Obtém lista de dimensões disponíveis."""
        if not self.agent_executor:
            raise RuntimeError("Agente não foi iniciado")
        
        return await self.chat("Quais dimensões estão disponíveis no GA4?")


# Função de conveniência para criar e iniciar o agente
async def create_ga4_agent(
    property_id: Optional[str] = None,
    openai_api_key: Optional[str] = None,
    model_name: str = "gpt-4",
    temperature: float = 0.1
) -> GA4Agent:
    """
    Cria e inicia um agente GA4.
    
    Args:
        property_id: ID da propriedade GA4
        openai_api_key: Chave da API OpenAI
        model_name: Nome do modelo
        temperature: Temperatura
        
    Returns:
        Agente GA4 iniciado
    """
    agent = GA4Agent(
        property_id=property_id,
        openai_api_key=openai_api_key,
        model_name=model_name,
        temperature=temperature
    )
    await agent.start()
    return agent


# Classe para uso em contexto (context manager)
class GA4AgentContext:
    """Context manager para o agente GA4."""
    
    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.agent = None
    
    async def __aenter__(self) -> GA4Agent:
        self.agent = await create_ga4_agent(**self.kwargs)
        return self.agent
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.agent:
            await self.agent.stop()


# Função para uso síncrono simples
def run_ga4_query(
    query: str,
    property_id: Optional[str] = None,
    openai_api_key: Optional[str] = None
) -> str:
    """
    Executa uma consulta simples no GA4.
    
    Args:
        query: Pergunta sobre os dados do GA4
        property_id: ID da propriedade GA4
        openai_api_key: Chave da API OpenAI
        
    Returns:
        Resposta da análise
    """
    async def _run():
        async with GA4AgentContext(
            property_id=property_id,
            openai_api_key=openai_api_key
        ) as agent:
            return await agent.chat(query)
    
    return asyncio.run(_run())
