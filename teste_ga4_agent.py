"""Script de teste para verificar se o agente GA4 está funcionando corretamente."""
import asyncio
import os
import sys
import traceback
from typing import Dict, List

from config_exemplo import config


async def teste_importacoes():
    """Testa se todas as importações estão funcionando."""
    print("🔍 Testando importações...")
    
    try:
        # Testar importações básicas
        import langchain
        print("✅ LangChain importado com sucesso")
        
        import langchain_openai
        print("✅ LangChain OpenAI importado com sucesso")
        
        from langchain_ga4_client import GA4MCPClient
        print("✅ Cliente GA4 MCP importado com sucesso")
        
        from langchain_ga4_tools import create_ga4_tools
        print("✅ Ferramentas GA4 importadas com sucesso")
        
        from ga4_agent import GA4Agent
        print("✅ Agente GA4 importado com sucesso")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("Execute: pip install langchain langchain-openai langchain-community")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False


async def teste_configuracao():
    """Testa se a configuração está correta."""
    print("\n🔧 Testando configuração...")
    
    # Verificar variáveis de ambiente
    openai_key = os.getenv("OPENAI_API_KEY")
    property_id = os.getenv("GA4_PROPERTY_ID")
    
    if not openai_key:
        print("❌ OPENAI_API_KEY não configurada")
        return False
    else:
        print("✅ OPENAI_API_KEY configurada")
    
    if not property_id:
        print("⚠️  GA4_PROPERTY_ID não configurada (opcional)")
    else:
        print("✅ GA4_PROPERTY_ID configurada")
    
    # Verificar arquivo de credenciais
    if os.path.exists("credenciais-ga4.json"):
        print("✅ Arquivo de credenciais encontrado")
    else:
        print("❌ Arquivo credenciais-ga4.json não encontrado")
        return False
    
    return True


async def teste_servidor_mcp():
    """Testa se o servidor MCP está funcionando."""
    print("\n🖥️  Testando servidor MCP...")
    
    try:
        from langchain_ga4_client import GA4MCPClient
        
        # Criar cliente
        client = GA4MCPClient(config.property_id)
        
        # Tentar iniciar
        await client.start()
        print("✅ Servidor MCP iniciado com sucesso")
        
        # Tentar listar ferramentas
        tools = await client.mcp_client.list_tools()
        print(f"✅ Ferramentas disponíveis: {len(tools)}")
        for tool in tools:
            print(f"  - {tool['name']}")
        
        # Parar cliente
        await client.stop()
        print("✅ Servidor MCP parado com sucesso")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no servidor MCP: {e}")
        traceback.print_exc()
        return False


async def teste_agente_basico():
    """Testa funcionalidade básica do agente."""
    print("\n🤖 Testando agente básico...")
    
    try:
        from ga4_agent import GA4AgentContext
        
        async with GA4AgentContext(
            property_id=config.property_id,
            openai_api_key=config.openai_api_key,
            model_name="gpt-3.5-turbo",  # Usar modelo mais barato para teste
            temperature=0.1
        ) as agent:
            
            print("✅ Agente iniciado com sucesso")
            
            # Teste simples - obter metadados
            print("🔍 Testando obtenção de metadados...")
            resposta = await agent.chat("Quais métricas estão disponíveis no GA4?")
            
            if resposta and len(resposta) > 50:  # Resposta razoável
                print("✅ Agente respondeu corretamente")
                print(f"📝 Resposta (primeiros 200 chars): {resposta[:200]}...")
                return True
            else:
                print("❌ Resposta muito curta ou vazia")
                return False
                
    except Exception as e:
        print(f"❌ Erro no agente: {e}")
        traceback.print_exc()
        return False


async def teste_consulta_dados():
    """Testa consulta real de dados."""
    print("\n📊 Testando consulta de dados...")
    
    try:
        from ga4_agent import GA4AgentContext
        
        async with GA4AgentContext(
            property_id=config.property_id,
            openai_api_key=config.openai_api_key,
            model_name="gpt-3.5-turbo",
            temperature=0.1
        ) as agent:
            
            # Teste de consulta simples
            print("🔍 Testando consulta de usuários ativos...")
            resposta = await agent.chat("Quantos usuários ativos tivemos ontem?")
            
            if resposta and "erro" not in resposta.lower():
                print("✅ Consulta de dados funcionou")
                print(f"📝 Resposta: {resposta[:300]}...")
                return True
            else:
                print("❌ Erro na consulta de dados")
                print(f"📝 Resposta: {resposta}")
                return False
                
    except Exception as e:
        print(f"❌ Erro na consulta: {e}")
        traceback.print_exc()
        return False


async def executar_todos_os_testes():
    """Executa todos os testes em sequência."""
    print("🧪 Iniciando testes do Agente GA4")
    print("=" * 50)
    
    resultados = {}
    
    # Lista de testes
    testes = [
        ("Importações", teste_importacoes),
        ("Configuração", teste_configuracao),
        ("Servidor MCP", teste_servidor_mcp),
        ("Agente Básico", teste_agente_basico),
        ("Consulta de Dados", teste_consulta_dados),
    ]
    
    # Executar testes
    for nome, teste_func in testes:
        try:
            resultado = await teste_func()
            resultados[nome] = resultado
        except Exception as e:
            print(f"❌ Erro no teste {nome}: {e}")
            resultados[nome] = False
    
    # Resumo dos resultados
    print("\n📋 Resumo dos Testes")
    print("=" * 30)
    
    sucessos = 0
    for nome, resultado in resultados.items():
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome}: {status}")
        if resultado:
            sucessos += 1
    
    print(f"\n📊 Resultado: {sucessos}/{len(testes)} testes passaram")
    
    if sucessos == len(testes):
        print("\n🎉 Todos os testes passaram! O agente está pronto para uso.")
        print("Execute: python exemplo_ga4_agent.py")
    else:
        print("\n⚠️  Alguns testes falharam. Verifique a configuração.")
        print("Consulte o README_AGENTE_GA4.md para instruções detalhadas.")
    
    return sucessos == len(testes)


def verificar_dependencias():
    """Verifica se as dependências estão instaladas."""
    print("📦 Verificando dependências...")
    
    dependencias = [
        "langchain",
        "langchain_openai", 
        "langchain_community",
        "langchain_core",
        "httpx",
        "pydantic",
        "openai"
    ]
    
    faltando = []
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep}")
            faltando.append(dep)
    
    if faltando:
        print(f"\n📥 Instale as dependências faltando:")
        print(f"pip install {' '.join(faltando)}")
        return False
    
    return True


async def main():
    """Função principal."""
    print("🎯 Teste do Agente GA4 com LangChain")
    print("=" * 40)
    
    # Verificar dependências primeiro
    if not verificar_dependencias():
        print("\n❌ Dependências faltando. Instale-as antes de continuar.")
        return
    
    # Verificar configuração básica
    config.print_config()
    
    if not config.validate():
        print("\n❌ Configuração inválida. Execute config_exemplo.py primeiro.")
        return
    
    # Executar testes
    print("\n🚀 Iniciando testes...")
    sucesso = await executar_todos_os_testes()
    
    if sucesso:
        print("\n✨ Sistema pronto para uso!")
    else:
        print("\n🔧 Corrija os problemas encontrados antes de usar o agente.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Teste interrompido pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        traceback.print_exc()
