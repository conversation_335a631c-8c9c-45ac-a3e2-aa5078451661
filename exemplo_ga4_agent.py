"""Exemplo de uso do agente GA4 com LangChain."""
import asyncio
import os
import sys
from typing import List

from ga4_agent import GA4AgentContext, run_ga4_query


async def exemplo_basico():
    """Exemplo básico de uso do agente GA4."""
    print("=== Exemplo Básico do Agente GA4 ===\n")
    
    # Configurar variáveis de ambiente (substitua pelos seus valores)
    property_id = os.getenv("GA4_PROPERTY_ID")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not property_id:
        print("⚠️  GA4_PROPERTY_ID não configurado. Usando valor padrão do servidor MCP.")
    
    if not openai_api_key:
        print("❌ OPENAI_API_KEY não configurado. Configure a variável de ambiente.")
        return
    
    # Usar o agente em contexto
    async with GA4AgentContext(
        property_id=property_id,
        openai_api_key=openai_api_key,
        model_name="gpt-4",
        temperature=0.1
    ) as agent:
        
        # Lista de perguntas de exemplo
        perguntas = [
            "Quais métricas estão disponíveis no GA4?",
            "Mostre-me os usuários ativos dos últimos 7 dias",
            "Qual é o número de sessões por país nos últimos 30 dias?",
            "Quantos usuários ativos temos agora em tempo real?",
            "Mostre-me as páginas mais visitadas esta semana",
            "Qual é a taxa de conversão dos últimos 30 dias?"
        ]
        
        for i, pergunta in enumerate(perguntas, 1):
            print(f"\n📊 Pergunta {i}: {pergunta}")
            print("-" * 60)
            
            try:
                resposta = await agent.chat(pergunta)
                print(f"🤖 Resposta:\n{resposta}")
            except Exception as e:
                print(f"❌ Erro: {e}")
            
            print("\n" + "="*80)


async def exemplo_conversa_interativa():
    """Exemplo de conversa interativa com o agente."""
    print("=== Conversa Interativa com o Agente GA4 ===\n")
    print("Digite suas perguntas sobre dados do GA4. Digite 'sair' para terminar.\n")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not openai_api_key:
        print("❌ OPENAI_API_KEY não configurado.")
        return
    
    async with GA4AgentContext(
        property_id=property_id,
        openai_api_key=openai_api_key
    ) as agent:
        
        chat_history = []
        
        while True:
            try:
                # Obter pergunta do usuário
                pergunta = input("\n🧑 Você: ").strip()
                
                if pergunta.lower() in ['sair', 'exit', 'quit']:
                    print("👋 Até logo!")
                    break
                
                if not pergunta:
                    continue
                
                # Processar pergunta
                print("🤖 Agente: Processando...")
                resposta = await agent.chat(pergunta, chat_history)
                print(f"🤖 Agente: {resposta}")
                
                # Atualizar histórico
                chat_history.extend([
                    {"role": "user", "content": pergunta},
                    {"role": "assistant", "content": resposta}
                ])
                
            except KeyboardInterrupt:
                print("\n👋 Até logo!")
                break
            except Exception as e:
                print(f"❌ Erro: {e}")


def exemplo_sincrono():
    """Exemplo de uso síncrono simples."""
    print("=== Exemplo Síncrono ===\n")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not openai_api_key:
        print("❌ OPENAI_API_KEY não configurado.")
        return
    
    # Perguntas simples
    perguntas = [
        "Quantos usuários ativos tivemos ontem?",
        "Mostre-me os dados de sessões por dispositivo dos últimos 7 dias"
    ]
    
    for pergunta in perguntas:
        print(f"\n📊 Pergunta: {pergunta}")
        print("-" * 50)
        
        try:
            resposta = run_ga4_query(
                query=pergunta,
                property_id=property_id,
                openai_api_key=openai_api_key
            )
            print(f"🤖 Resposta: {resposta}")
        except Exception as e:
            print(f"❌ Erro: {e}")


async def exemplo_analises_especificas():
    """Exemplos de análises específicas de negócio."""
    print("=== Análises Específicas de Negócio ===\n")
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    if not openai_api_key:
        print("❌ OPENAI_API_KEY não configurado.")
        return
    
    async with GA4AgentContext(
        property_id=property_id,
        openai_api_key=openai_api_key
    ) as agent:
        
        # Análises de negócio
        analises = [
            {
                "titulo": "Análise de Audiência",
                "pergunta": "Faça uma análise completa da audiência dos últimos 30 dias, incluindo usuários ativos, sessões e dados demográficos"
            },
            {
                "titulo": "Análise de Canais de Aquisição",
                "pergunta": "Quais são os principais canais de aquisição de usuários e qual o desempenho de cada um?"
            },
            {
                "titulo": "Análise de Comportamento",
                "pergunta": "Analise o comportamento dos usuários: páginas mais visitadas, tempo de sessão e taxa de rejeição"
            },
            {
                "titulo": "Análise de Conversões",
                "pergunta": "Mostre-me dados de conversões e receita dos últimos 30 dias, com insights sobre performance"
            },
            {
                "titulo": "Análise em Tempo Real",
                "pergunta": "Qual é a situação atual do site em tempo real? Usuários ativos, páginas sendo visitadas, etc."
            }
        ]
        
        for analise in analises:
            print(f"\n📈 {analise['titulo']}")
            print("=" * 60)
            print(f"Pergunta: {analise['pergunta']}")
            print("-" * 60)
            
            try:
                resposta = await agent.chat(analise['pergunta'])
                print(f"🤖 Análise:\n{resposta}")
            except Exception as e:
                print(f"❌ Erro: {e}")
            
            print("\n" + "="*80)


def mostrar_menu():
    """Mostra o menu de opções."""
    print("\n🚀 Agente GA4 com LangChain")
    print("=" * 40)
    print("1. Exemplo básico")
    print("2. Conversa interativa")
    print("3. Exemplo síncrono")
    print("4. Análises específicas")
    print("5. Sair")
    print("=" * 40)


async def main():
    """Função principal."""
    print("🎯 Bem-vindo ao Agente GA4 com LangChain!")
    print("\nEste agente permite fazer perguntas em linguagem natural sobre seus dados do Google Analytics 4.")
    print("\n📋 Configuração necessária:")
    print("- GA4_PROPERTY_ID: ID da sua propriedade GA4 (opcional)")
    print("- OPENAI_API_KEY: Sua chave da API OpenAI (obrigatório)")
    print("- Credenciais do GA4 em 'credenciais-ga4.json'")
    
    while True:
        mostrar_menu()
        
        try:
            opcao = input("\nEscolha uma opção (1-5): ").strip()
            
            if opcao == "1":
                await exemplo_basico()
            elif opcao == "2":
                await exemplo_conversa_interativa()
            elif opcao == "3":
                exemplo_sincrono()
            elif opcao == "4":
                await exemplo_analises_especificas()
            elif opcao == "5":
                print("👋 Até logo!")
                break
            else:
                print("❌ Opção inválida. Tente novamente.")
                
        except KeyboardInterrupt:
            print("\n👋 Até logo!")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")


if __name__ == "__main__":
    # Verificar se as dependências estão instaladas
    try:
        import langchain
        import openai
    except ImportError as e:
        print(f"❌ Dependência não encontrada: {e}")
        print("Execute: pip install langchain langchain-openai langchain-community")
        sys.exit(1)
    
    # Executar programa principal
    asyncio.run(main())
