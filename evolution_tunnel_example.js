/**
 * Exemplo de uso do GA4 Python MCP com Cloudflare Tunnel na Evolution API
 * 
 * Este script mostra como usar o túnel Cloudflare para acessar o servidor GA4
 * de forma pública e segura.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

/**
 * Função principal para Evolution API com suporte a túnel Cloudflare
 * 
 * @param {Object} input - Parâmetros de entrada
 * @param {string} input.tunnel_mode - "auto", "manual", "local" (padrão: "auto")
 * @param {string} input.tunnel_url - URL manual do túnel (se tunnel_mode = "manual")
 * @param {string} input.tunnel_info_file - Caminho para tunnel_info.json (padrão: "./tunnel_info.json")
 * @param {string} input.property_id - ID da propriedade GA4
 * @param {string} input.action - Ação a executar
 * @param {Array} input.metrics - Métricas GA4
 * @param {Array} input.dimensions - Dimensões GA4
 * @param {string} input.date_range - Período dos dados
 * @param {number} input.limit - Limite de registros
 * @param {Object} context - Contexto da Evolution
 */
module.exports = async function(input, context) {
  try {
    console.log('🌐 GA4 Python MCP - Cloudflare Tunnel Integration');
    
    // Configurações
    const config = {
      tunnel_mode: input.tunnel_mode || "auto",
      tunnel_url: input.tunnel_url || null,
      tunnel_info_file: input.tunnel_info_file || "./tunnel_info.json",
      property_id: input.property_id || "330715799",
      action: input.action || "report",
      metrics: input.metrics || ["activeUsers"],
      dimensions: input.dimensions || null,
      date_range: input.date_range || "last30days",
      limit: input.limit || 10
    };
    
    console.log(`📊 Modo do túnel: ${config.tunnel_mode}`);
    
    // Determina a URL do servidor
    let serverUrl = await getServerUrl(config);
    
    if (!serverUrl) {
      throw new Error('Não foi possível determinar a URL do servidor');
    }
    
    console.log(`🔗 Usando servidor: ${serverUrl}`);
    
    // Faz a requisição
    const response = await makeGA4Request(serverUrl, config);
    
    console.log('✅ Dados obtidos com sucesso');
    
    return {
      success: true,
      data: response.data,
      server_url: serverUrl,
      tunnel_mode: config.tunnel_mode,
      source: "ga4-python-mcp-tunnel",
      timestamp: new Date().toISOString(),
      config: config
    };
    
  } catch (error) {
    console.error('❌ Erro na integração:', error.message);
    
    return {
      success: false,
      error: error.message,
      source: "ga4-python-mcp-tunnel",
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Determina a URL do servidor baseado no modo configurado
 */
async function getServerUrl(config) {
  switch (config.tunnel_mode) {
    case "manual":
      // URL fornecida manualmente
      if (!config.tunnel_url) {
        throw new Error('tunnel_url é obrigatório quando tunnel_mode = "manual"');
      }
      return config.tunnel_url;
      
    case "local":
      // Servidor local
      return "http://localhost:8080";
      
    case "auto":
    default:
      // Tenta detectar automaticamente
      return await detectServerUrl(config);
  }
}

/**
 * Detecta automaticamente a URL do servidor
 */
async function detectServerUrl(config) {
  console.log('🔍 Detectando URL do servidor...');
  
  // 1. Tenta ler arquivo tunnel_info.json
  try {
    if (fs.existsSync(config.tunnel_info_file)) {
      const tunnelInfo = JSON.parse(fs.readFileSync(config.tunnel_info_file, 'utf8'));
      
      if (tunnelInfo.tunnel_url && tunnelInfo.status === 'active') {
        console.log('📄 URL encontrada em tunnel_info.json');
        
        // Testa se o túnel está funcionando
        if (await testServerUrl(tunnelInfo.tunnel_url)) {
          return tunnelInfo.tunnel_url;
        } else {
          console.log('⚠️ Túnel em tunnel_info.json não está respondendo');
        }
      }
    }
  } catch (error) {
    console.log('⚠️ Erro ao ler tunnel_info.json:', error.message);
  }
  
  // 2. Tenta servidor local
  console.log('🏠 Testando servidor local...');
  if (await testServerUrl("http://localhost:8080")) {
    console.log('✅ Servidor local encontrado');
    return "http://localhost:8080";
  }
  
  // 3. Falha
  throw new Error('Nenhum servidor disponível. Inicie o túnel com: python cloudflare_tunnel.py');
}

/**
 * Testa se uma URL do servidor está funcionando
 */
async function testServerUrl(url) {
  try {
    const response = await axios.get(`${url}/health`, { 
      timeout: 5000,
      validateStatus: (status) => status === 200
    });
    
    return response.data && response.data.status === 'ok';
  } catch (error) {
    return false;
  }
}

/**
 * Faz a requisição para o servidor GA4
 */
async function makeGA4Request(serverUrl, config) {
  const requestData = {
    property_id: config.property_id,
    action: config.action,
    metrics: config.metrics,
    dimensions: config.dimensions,
    date_range: config.date_range,
    limit: config.limit
  };
  
  console.log('📊 Fazendo requisição GA4...');
  
  const response = await axios.post(
    `${serverUrl}/ga4-evolution`,
    requestData,
    {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Evolution-API-GA4-Client'
      }
    }
  );
  
  if (!response.data.success) {
    throw new Error(response.data.error || 'Erro desconhecido do servidor');
  }
  
  return response.data;
}

/**
 * Função utilitária para obter informações do túnel
 */
async function getTunnelInfo(tunnelInfoFile = "./tunnel_info.json") {
  try {
    if (fs.existsSync(tunnelInfoFile)) {
      return JSON.parse(fs.readFileSync(tunnelInfoFile, 'utf8'));
    }
    return null;
  } catch (error) {
    console.error('Erro ao ler tunnel_info.json:', error.message);
    return null;
  }
}

/**
 * Função utilitária para aguardar túnel ficar disponível
 */
async function waitForTunnel(maxWaitSeconds = 60, tunnelInfoFile = "./tunnel_info.json") {
  console.log(`⏳ Aguardando túnel ficar disponível (máximo ${maxWaitSeconds}s)...`);
  
  const startTime = Date.now();
  
  while ((Date.now() - startTime) < (maxWaitSeconds * 1000)) {
    const tunnelInfo = await getTunnelInfo(tunnelInfoFile);
    
    if (tunnelInfo && tunnelInfo.tunnel_url && tunnelInfo.status === 'active') {
      if (await testServerUrl(tunnelInfo.tunnel_url)) {
        console.log(`✅ Túnel disponível: ${tunnelInfo.tunnel_url}`);
        return tunnelInfo.tunnel_url;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000)); // Aguarda 2 segundos
  }
  
  throw new Error(`Timeout: túnel não ficou disponível em ${maxWaitSeconds} segundos`);
}

// Exporta funções utilitárias
module.exports.getTunnelInfo = getTunnelInfo;
module.exports.waitForTunnel = waitForTunnel;
module.exports.testServerUrl = testServerUrl;

// Exemplo de uso para teste
if (require.main === module) {
  async function testExample() {
    console.log('🧪 Testando integração com túnel...');
    
    // Exemplo 1: Modo automático
    console.log('\n1. Teste modo automático:');
    let result = await module.exports({
      tunnel_mode: "auto",
      property_id: "330715799",
      action: "report",
      metrics: ["activeUsers"],
      date_range: "last7days",
      limit: 3
    }, {});
    
    console.log('Resultado:', JSON.stringify(result, null, 2));
    
    // Exemplo 2: Modo local
    console.log('\n2. Teste modo local:');
    result = await module.exports({
      tunnel_mode: "local",
      property_id: "330715799",
      action: "realtime",
      metrics: ["activeUsers"],
      limit: 1
    }, {});
    
    console.log('Resultado:', JSON.stringify(result, null, 2));
    
    // Exemplo 3: Informações do túnel
    console.log('\n3. Informações do túnel:');
    const tunnelInfo = await getTunnelInfo();
    console.log('Tunnel Info:', JSON.stringify(tunnelInfo, null, 2));
  }
  
  testExample().catch(console.error);
}
