"""
Servidor HTTP simples para integração com Evolution API.
Substitui completamente o servidor Node.js.
"""
import asyncio
import json
import logging
from datetime import datetime
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import parse_qs, urlparse
import threading
import os

from evolution_integration import get_ga4_data, get_ga4_metadata, format_for_evolution

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ga4-http-server")


class GA4HTTPHandler(BaseHTTPRequestHandler):
    """Handler HTTP para requisições GA4."""
    
    def do_GET(self):
        """Processa requisições GET."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/health':
            self.send_health_check()
        elif parsed_path.path == '/':
            self.send_welcome()
        else:
            self.send_error(404, "Endpoint não encontrado")
    
    def do_POST(self):
        """Processa requisições POST."""
        parsed_path = urlparse(self.path)
        
        try:
            # Lê o corpo da requisição
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            if parsed_path.path == '/ga4-data':
                self.handle_ga4_data(post_data)
            elif parsed_path.path == '/ga4-metadata':
                self.handle_ga4_metadata(post_data)
            elif parsed_path.path == '/ga4-evolution':
                self.handle_evolution_request(post_data)
            else:
                self.send_error(404, "Endpoint não encontrado")
                
        except Exception as e:
            logger.error(f"Erro ao processar POST: {e}")
            self.send_json_response({
                "success": False,
                "error": str(e)
            }, 500)
    
    def handle_ga4_data(self, post_data):
        """Processa requisições de dados GA4."""
        try:
            data = json.loads(post_data.decode())
            
            property_id = data.get('property_id', '330715799')
            metrics = data.get('metrics', ['activeUsers'])
            dimensions = data.get('dimensions')
            date_range = data.get('date_range', 'last30days')
            limit = data.get('limit', 10)
            realtime = data.get('realtime', False)
            
            # Executa a consulta de forma assíncrona
            result = self.run_async(get_ga4_data(
                property_id=property_id,
                metrics=metrics,
                dimensions=dimensions,
                date_range=date_range,
                limit=limit,
                realtime=realtime
            ))
            
            self.send_json_response(result)
            
        except Exception as e:
            logger.error(f"Erro ao obter dados GA4: {e}")
            self.send_json_response({
                "success": False,
                "error": str(e)
            }, 500)
    
    def handle_ga4_metadata(self, post_data):
        """Processa requisições de metadados GA4."""
        try:
            data = json.loads(post_data.decode())
            
            property_id = data.get('property_id', '330715799')
            metadata_type = data.get('metadata_type', 'all')
            
            result = self.run_async(get_ga4_metadata(property_id, metadata_type))
            
            self.send_json_response(result)
            
        except Exception as e:
            logger.error(f"Erro ao obter metadados: {e}")
            self.send_json_response({
                "success": False,
                "error": str(e)
            }, 500)
    
    def handle_evolution_request(self, post_data):
        """Processa requisições da Evolution API."""
        try:
            data = json.loads(post_data.decode())
            
            # Importa a função principal
            from evolution_integration import main_evolution_function
            
            result_text = self.run_async(main_evolution_function(data))
            
            self.send_json_response({
                "success": True,
                "data": result_text,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Erro na requisição Evolution: {e}")
            self.send_json_response({
                "success": False,
                "error": str(e)
            }, 500)
    
    def send_health_check(self):
        """Envia resposta de health check."""
        self.send_json_response({
            "status": "ok",
            "service": "GA4 Python MCP Server",
            "timestamp": datetime.now().isoformat()
        })
    
    def send_welcome(self):
        """Envia página de boas-vindas."""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>GA4 Python MCP Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; }
                .method { color: #007acc; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>🚀 GA4 Python MCP Server</h1>
            <p>Servidor HTTP para integração com Google Analytics 4 via Evolution API.</p>
            
            <h2>📋 Endpoints Disponíveis</h2>
            
            <div class="endpoint">
                <span class="method">GET</span> /health - Health check do servidor
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /ga4-data - Obter dados GA4
                <br><small>Body: {"property_id", "metrics", "dimensions", "date_range", "limit", "realtime"}</small>
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /ga4-metadata - Obter metadados GA4
                <br><small>Body: {"property_id", "metadata_type"}</small>
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /ga4-evolution - Endpoint para Evolution API
                <br><small>Body: {"property_id", "action", "metrics", "dimensions", etc.}</small>
            </div>
            
            <h2>🔧 Configuração</h2>
            <p>Certifique-se de que o arquivo <code>credenciais-ga4.json</code> está presente no diretório.</p>
            
            <h2>📊 Exemplo de Uso</h2>
            <pre>
curl -X POST http://localhost:8080/ga4-data \\
  -H "Content-Type: application/json" \\
  -d '{
    "property_id": "330715799",
    "metrics": ["activeUsers", "sessions"],
    "dimensions": ["date"],
    "date_range": "last7days",
    "limit": 10
  }'
            </pre>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_json_response(self, data, status_code=200):
        """Envia resposta JSON."""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def run_async(self, coro):
        """Executa uma corrotina de forma síncrona."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    def log_message(self, format, *args):
        """Override para usar nosso logger."""
        logger.info(f"{self.address_string()} - {format % args}")


def run_server(host='localhost', port=8080):
    """Executa o servidor HTTP."""
    server_address = (host, port)
    httpd = HTTPServer(server_address, GA4HTTPHandler)
    
    logger.info(f"🚀 Servidor GA4 iniciado em http://{host}:{port}")
    logger.info("📋 Endpoints disponíveis:")
    logger.info("   GET  /health        - Health check")
    logger.info("   POST /ga4-data      - Dados GA4")
    logger.info("   POST /ga4-metadata  - Metadados GA4")
    logger.info("   POST /ga4-evolution - Evolution API")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("🛑 Servidor interrompido pelo usuário")
        httpd.shutdown()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Servidor HTTP GA4 Python MCP")
    parser.add_argument("--host", default="localhost", help="Host do servidor")
    parser.add_argument("--port", type=int, default=8080, help="Porta do servidor")
    
    args = parser.parse_args()
    
    # Configura variáveis de ambiente
    if not os.environ.get("GOOGLE_APPLICATION_CREDENTIALS"):
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "credenciais-ga4.json"
    
    run_server(args.host, args.port)
