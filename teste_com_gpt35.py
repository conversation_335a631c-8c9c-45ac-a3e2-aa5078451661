"""Teste do agente GA4 usando GPT-3.5-turbo (mais barato)."""
import asyncio
import os
from ga4_agent import GA4AgentContext


async def teste_simples():
    """Teste simples com GPT-3.5-turbo."""
    print("🧪 Teste Simples com GPT-3.5-turbo")
    print("=" * 40)
    
    property_id = os.getenv("GA4_PROPERTY_ID")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not openai_key:
        print("❌ OPENAI_API_KEY não configurada")
        return
    
    try:
        async with GA4AgentContext(
            property_id=property_id,
            openai_api_key=openai_key,
            model_name="gpt-3.5-turbo",  # Modelo mais barato
            temperature=0.1
        ) as agent:
            
            print("✅ Agente iniciado com GPT-3.5-turbo")
            
            # Teste simples
            pergunta = "Quais métricas estão disponíveis no GA4?"
            print(f"\n🔍 Pergunta: {pergunta}")
            
            resposta = await agent.chat(pergunta)
            print(f"\n🤖 Resposta:")
            print("-" * 50)
            print(resposta)
            print("-" * 50)
            
            if "erro" not in resposta.lower() and len(resposta) > 50:
                print("\n✅ Teste bem-sucedido!")
                return True
            else:
                print("\n❌ Teste falhou")
                return False
                
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        return False


async def main():
    """Função principal."""
    print("🎯 Teste com GPT-3.5-turbo (Modelo Mais Barato)")
    print("=" * 50)
    print("Este teste usa GPT-3.5-turbo que consome menos créditos.")
    print()
    
    sucesso = await teste_simples()
    
    if sucesso:
        print("\n🎉 Sistema funcionando! Você pode usar o agente.")
        print("\nPara usar:")
        print("1. Execute: agente_ga4.bat")
        print("2. Ou use: python exemplo_ga4_agent.py")
    else:
        print("\n⚠️  Verifique sua quota OpenAI em:")
        print("https://platform.openai.com/account/billing")


if __name__ == "__main__":
    asyncio.run(main())
